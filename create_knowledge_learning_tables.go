package main

import (
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🧠 创建知识学习相关表...")

	// 创建学习知识表
	createLearnedKnowledgeTable := `
		CREATE TABLE IF NOT EXISTS learned_knowledge (
			id INT AUTO_INCREMENT PRIMARY KEY,
			question TEXT NOT NULL COMMENT '学习到的问题',
			answer TEXT NOT NULL COMMENT '学习到的答案',
			source ENUM('user_input', 'conversation', 'correction', 'implicit') NOT NULL COMMENT '知识来源',
			confidence FLOAT DEFAULT 0.0 COMMENT '知识置信度',
			category VARCHAR(100) DEFAULT 'general' COMMENT '知识分类',
			keywords JSON COMMENT '关键词',
			context TEXT COMMENT '学习上下文',
			learned_from VARCHAR(100) COMMENT '学习来源用户',
			status ENUM('pending', 'approved', 'rejected', 'integrated') DEFAULT 'approved' COMMENT '状态',
			metadata JSON COMMENT '元数据',
			usage_count INT DEFAULT 0 COMMENT '使用次数',
			success_rate FLOAT DEFAULT 0.0 COMMENT '成功率',
			last_used TIMESTAMP NULL COMMENT '最后使用时间',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_source (source),
			INDEX idx_category (category),
			INDEX idx_status (status),
			INDEX idx_confidence (confidence),
			INDEX idx_learned_from (learned_from),
			INDEX idx_created_at (created_at),
			FULLTEXT KEY ft_question (question),
			FULLTEXT KEY ft_answer (answer)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习知识表'
	`

	// 创建知识向量表
	createKnowledgeVectorsTable := `
		CREATE TABLE IF NOT EXISTS knowledge_vectors (
			id INT AUTO_INCREMENT PRIMARY KEY,
			knowledge_id INT NOT NULL COMMENT '知识ID',
			vector_data JSON NOT NULL COMMENT '向量数据',
			vector_type ENUM('question', 'answer', 'combined') DEFAULT 'combined' COMMENT '向量类型',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_knowledge_id (knowledge_id),
			INDEX idx_vector_type (vector_type),
			FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识向量表'
	`

	// 创建知识反馈表
	createKnowledgeFeedbackTable := `
		CREATE TABLE IF NOT EXISTS knowledge_feedback (
			id INT AUTO_INCREMENT PRIMARY KEY,
			knowledge_id INT NOT NULL COMMENT '知识ID',
			user_id VARCHAR(100) NOT NULL COMMENT '用户ID',
			feedback_type ENUM('helpful', 'not_helpful', 'incorrect', 'incomplete') NOT NULL COMMENT '反馈类型',
			rating INT DEFAULT 0 COMMENT '评分 1-5',
			comment TEXT COMMENT '反馈评论',
			context TEXT COMMENT '反馈上下文',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_knowledge_id (knowledge_id),
			INDEX idx_user_id (user_id),
			INDEX idx_feedback_type (feedback_type),
			INDEX idx_rating (rating),
			FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识反馈表'
	`

	// 创建知识使用记录表
	createKnowledgeUsageTable := `
		CREATE TABLE IF NOT EXISTS knowledge_usage (
			id INT AUTO_INCREMENT PRIMARY KEY,
			knowledge_id INT NOT NULL COMMENT '知识ID',
			query_id BIGINT COMMENT '查询ID',
			user_id VARCHAR(100) NOT NULL COMMENT '用户ID',
			match_score FLOAT DEFAULT 0.0 COMMENT '匹配分数',
			was_helpful BOOLEAN DEFAULT NULL COMMENT '是否有帮助',
			response_time INT DEFAULT 0 COMMENT '响应时间(ms)',
			context TEXT COMMENT '使用上下文',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_knowledge_id (knowledge_id),
			INDEX idx_query_id (query_id),
			INDEX idx_user_id (user_id),
			INDEX idx_match_score (match_score),
			INDEX idx_was_helpful (was_helpful),
			INDEX idx_created_at (created_at),
			FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识使用记录表'
	`

	// 执行表创建
	tables := map[string]string{
		"learned_knowledge":  createLearnedKnowledgeTable,
		"knowledge_vectors":  createKnowledgeVectorsTable,
		"knowledge_feedback": createKnowledgeFeedbackTable,
		"knowledge_usage":    createKnowledgeUsageTable,
	}

	for tableName, createSQL := range tables {
		fmt.Printf("📋 创建表: %s\n", tableName)
		if _, err := db.Exec(createSQL); err != nil {
			log.Fatalf("Failed to create table %s: %v", tableName, err)
		}
		fmt.Printf("✅ 表 %s 创建成功\n", tableName)
	}

	// 插入一些示例学习知识
	fmt.Println("📝 插入示例学习知识...")

	sampleKnowledge := []string{
		`INSERT IGNORE INTO learned_knowledge 
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES 
		('什么是C#？', 'C#是微软开发的一种面向对象的编程语言，运行在.NET框架上。', 'user_input', 0.9, 'technology', '["c#", "编程语言", "微软", ".net"]', '用户教学输入', 'system', 'approved')`,

		`INSERT IGNORE INTO learned_knowledge 
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES 
		('什么是Python？', 'Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。', 'user_input', 0.9, 'technology', '["python", "编程语言", "高级语言"]', '用户教学输入', 'system', 'approved')`,

		`INSERT IGNORE INTO learned_knowledge 
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES 
		('什么是JavaScript？', 'JavaScript是一种主要用于网页开发的脚本语言，可以实现动态交互效果。', 'user_input', 0.9, 'technology', '["javascript", "脚本语言", "网页开发"]', '用户教学输入', 'system', 'approved')`,

		`INSERT IGNORE INTO learned_knowledge 
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES 
		('什么是Java？', 'Java是一种跨平台的面向对象编程语言，广泛用于企业级应用开发。', 'user_input', 0.9, 'technology', '["java", "编程语言", "跨平台", "企业级"]', '用户教学输入', 'system', 'approved')`,
	}

	for i, knowledgeSQL := range sampleKnowledge {
		if _, err := db.Exec(knowledgeSQL); err != nil {
			log.Printf("Warning: Failed to insert sample knowledge %d: %v", i+1, err)
		} else {
			fmt.Printf("✅ 插入示例知识 %d/4\n", i+1)
		}
	}

	fmt.Println("\n🎉 知识学习系统数据库初始化完成！")
	fmt.Println("📊 创建的表:")
	fmt.Println("  • learned_knowledge - 学习到的知识")
	fmt.Println("  • knowledge_vectors - 知识向量")
	fmt.Println("  • knowledge_feedback - 知识反馈")
	fmt.Println("  • knowledge_usage - 知识使用记录")
	fmt.Println("\n🧠 系统现在可以从用户输入中学习新知识！")
}
