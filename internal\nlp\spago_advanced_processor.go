package nlp

import (
	"log"
	"math"
	"sort"
	"strings"
	"sync"
)

// SpagoAdvancedProcessor 基于Spago的高级NLP处理器
type SpagoAdvancedProcessor struct {
	// 基础组件
	vocabulary map[string]int
	embeddings map[string][]float64
	categories []string

	// 模型组件
	textClassifier    *TextClassificationModel
	sentimentAnalyzer *SentimentAnalysisModel
	entityRecognizer  *EntityRecognitionModel

	// 状态管理
	initialized bool
	mutex       sync.RWMutex
}

// TextClassificationModel 文本分类模型
type TextClassificationModel struct {
	weights    [][]float64
	biases     []float64
	categories []string
	inputDim   int
	hiddenDim  int
	outputDim  int
}

// SentimentAnalysisModel 情感分析模型
type SentimentAnalysisModel struct {
	weights      [][]float64
	biases       []float64
	inputDim     int
	hiddenDim    int
	outputDim    int
	sentimentMap map[int]string
}

// EntityRecognitionModel 实体识别模型
type EntityRecognitionModel struct {
	weights     [][]float64
	biases      []float64
	entityTypes []string
	inputDim    int
	outputDim   int
}

// SpagoResult Spago处理结果
type SpagoResult struct {
	// 词向量和特征
	TokenEmbeddings   [][]float64        `json:"token_embeddings"`   // 词向量
	SentenceEmbedding []float64          `json:"sentence_embedding"` // 句子向量
	Features          map[string]float64 `json:"features"`           // 特征向量

	// 分类结果
	Classifications []ClassificationInfo `json:"classifications"` // 文本分类
	SentimentScore  float64              `json:"sentiment_score"` // 情感分数
	SentimentLabel  string               `json:"sentiment_label"` // 情感标签
	SentimentProbs  []float64            `json:"sentiment_probs"` // 情感概率分布

	// 实体识别
	Entities []EntityPrediction `json:"entities"` // 实体预测

	// 质量指标
	Confidence     float64 `json:"confidence"`      // 整体置信度
	ProcessingTime float64 `json:"processing_time"` // 处理时间(ms)
	ModelVersion   string  `json:"model_version"`   // 模型版本
}

// EntityPrediction 实体预测结果
type EntityPrediction struct {
	Text          string    `json:"text"`          // 实体文本
	Label         string    `json:"label"`         // 实体标签
	Confidence    float64   `json:"confidence"`    // 置信度
	StartPos      int       `json:"start_pos"`     // 开始位置
	EndPos        int       `json:"end_pos"`       // 结束位置
	Probabilities []float64 `json:"probabilities"` // 各类别概率
}

// NewSpagoAdvancedProcessor 创建Spago高级处理器
func NewSpagoAdvancedProcessor() *SpagoAdvancedProcessor {
	log.Printf("🚀 初始化Spago高级NLP处理器")

	processor := &SpagoAdvancedProcessor{
		vocabulary:  make(map[string]int),
		embeddings:  make(map[string][]float64),
		categories:  []string{"娱乐", "科技", "体育", "财经", "社会", "文学", "影视", "综合"},
		initialized: false,
	}

	// 异步初始化模型
	go processor.initializeModels()

	return processor
}

// initializeModels 初始化所有模型
func (sap *SpagoAdvancedProcessor) initializeModels() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ Spago模型初始化失败: %v", r)
			sap.initialized = false
		}
	}()

	log.Printf("🔧 开始初始化Spago模型...")

	// 1. 初始化词汇表和词向量
	sap.initializeVocabularyAndEmbeddings()

	// 2. 初始化文本分类模型
	sap.initializeTextClassifier()

	// 3. 初始化情感分析模型
	sap.initializeSentimentAnalyzer()

	// 4. 初始化实体识别模型
	sap.initializeEntityRecognizer()

	sap.mutex.Lock()
	sap.initialized = true
	sap.mutex.Unlock()

	log.Printf("✅ Spago高级处理器初始化完成")
}

// ProcessText 处理文本
func (sap *SpagoAdvancedProcessor) ProcessText(text string) *SpagoResult {
	startTime := getCurrentTimeMs()

	sap.mutex.RLock()
	isInitialized := sap.initialized
	sap.mutex.RUnlock()

	if !isInitialized {
		log.Printf("⚠️ Spago处理器未完全初始化，使用基础处理")
		return sap.basicProcess(text)
	}

	log.Printf("🧠 Spago处理文本: %s", text[:min(len(text), 50)])

	result := &SpagoResult{
		Features:     make(map[string]float64),
		ModelVersion: "spago-v1.0",
	}

	// 1. 文本预处理和分词
	tokens := sap.preprocessAndTokenize(text)
	log.Printf("   分词结果: %d 个token", len(tokens))

	// 2. 生成词向量
	tokenEmbeddings := sap.generateTokenEmbeddings(tokens)
	result.TokenEmbeddings = tokenEmbeddings
	log.Printf("   生成词向量: %d 个", len(tokenEmbeddings))

	// 3. 生成句子向量
	sentenceEmbedding := sap.generateSentenceEmbedding(tokenEmbeddings)
	result.SentenceEmbedding = sentenceEmbedding
	log.Printf("   句子向量维度: %d", len(sentenceEmbedding))

	// 4. 文本分类
	classifications := sap.classifyText(sentenceEmbedding)
	result.Classifications = classifications
	log.Printf("   分类结果: %d 个类别", len(classifications))

	// 5. 情感分析
	sentimentScore, sentimentLabel, sentimentProbs := sap.analyzeSentiment(sentenceEmbedding)
	result.SentimentScore = sentimentScore
	result.SentimentLabel = sentimentLabel
	result.SentimentProbs = sentimentProbs
	log.Printf("   情感分析: %s (%.3f)", sentimentLabel, sentimentScore)

	// 6. 实体识别
	entities := sap.recognizeEntities(tokens, tokenEmbeddings)
	result.Entities = entities
	log.Printf("   实体识别: %d 个实体", len(entities))

	// 7. 提取高级特征
	features := sap.extractAdvancedFeatures(text, tokenEmbeddings, sentenceEmbedding)
	result.Features = features
	log.Printf("   高级特征: %d 个", len(features))

	// 8. 计算综合置信度
	confidence := sap.calculateOverallConfidence(result)
	result.Confidence = confidence

	// 9. 记录处理时间
	result.ProcessingTime = getCurrentTimeMs() - startTime

	log.Printf("✅ Spago处理完成，置信度: %.3f, 耗时: %.2fms", confidence, result.ProcessingTime)
	return result
}

// initializeVocabularyAndEmbeddings 初始化词汇表和词向量
func (sap *SpagoAdvancedProcessor) initializeVocabularyAndEmbeddings() {
	log.Printf("   🔧 初始化词汇表和词向量...")

	// 构建中文NLP词汇表
	vocabularyWords := []string{
		// 基础词汇
		"热搜", "排行", "榜单", "话题", "事件", "新闻", "消息", "资讯",
		"明星", "演员", "歌手", "导演", "主演", "艺人", "偶像", "网红",
		"电影", "电视剧", "影片", "剧集", "动画", "纪录片", "综艺", "节目",
		"小说", "作者", "作品", "文学", "书籍", "写作", "创作", "出版",
		"科技", "技术", "AI", "人工智能", "互联网", "数码", "手机", "电脑",
		"体育", "比赛", "运动", "球员", "赛事", "足球", "篮球", "奥运",
		"经济", "股票", "金融", "投资", "市场", "公司", "企业", "商业",
		"社会", "政策", "民生", "国家", "政府", "公共", "服务", "法律",

		// 情感词汇
		"好", "棒", "优秀", "精彩", "成功", "喜欢", "爱", "开心", "满意",
		"坏", "差", "失败", "糟糕", "讨厌", "恨", "愤怒", "失望", "不满",
		"中性", "一般", "普通", "正常", "平常", "还行", "可以", "尚可",

		// 实体词汇
		"人", "地方", "机构", "产品", "品牌", "时间", "数字", "其他",
	}

	// 构建词汇表索引
	for i, word := range vocabularyWords {
		sap.vocabulary[word] = i
	}

	// 生成预训练词向量（使用改进的哈希方法）
	embeddingDim := 128
	for word, idx := range sap.vocabulary {
		embedding := sap.generatePretrainedEmbedding(word, idx, embeddingDim)
		sap.embeddings[word] = embedding
	}

	log.Printf("   ✅ 词汇表初始化完成: %d 个词汇, %d 维词向量", len(sap.vocabulary), embeddingDim)
}

// initializeTextClassifier 初始化文本分类模型
func (sap *SpagoAdvancedProcessor) initializeTextClassifier() {
	log.Printf("   🔧 初始化文本分类模型...")

	inputDim := 128                  // 句子向量维度
	hiddenDim := 64                  // 隐藏层维度
	outputDim := len(sap.categories) // 输出类别数

	classifier := &TextClassificationModel{
		categories: sap.categories,
		inputDim:   inputDim,
		hiddenDim:  hiddenDim,
		outputDim:  outputDim,
	}

	// 初始化权重矩阵（使用Xavier初始化）
	classifier.weights = make([][]float64, 2)                                // 两层网络
	classifier.weights[0] = sap.initializeWeightMatrix(inputDim, hiddenDim)  // 输入到隐藏层
	classifier.weights[1] = sap.initializeWeightMatrix(hiddenDim, outputDim) // 隐藏层到输出层

	// 初始化偏置
	classifier.biases = make([]float64, hiddenDim+outputDim)
	for i := range classifier.biases {
		classifier.biases[i] = sap.randomNormal(0, 0.1)
	}

	sap.textClassifier = classifier
	log.Printf("   ✅ 文本分类模型初始化完成: %d->%d->%d", inputDim, hiddenDim, outputDim)
}

// initializeSentimentAnalyzer 初始化情感分析模型
func (sap *SpagoAdvancedProcessor) initializeSentimentAnalyzer() {
	log.Printf("   🔧 初始化情感分析模型...")

	inputDim := 128 // 句子向量维度
	hiddenDim := 32 // 隐藏层维度
	outputDim := 3  // 3个情感类别：积极、消极、中性

	analyzer := &SentimentAnalysisModel{
		inputDim:  inputDim,
		hiddenDim: hiddenDim,
		outputDim: outputDim,
		sentimentMap: map[int]string{
			0: "消极",
			1: "中性",
			2: "积极",
		},
	}

	// 初始化权重矩阵
	analyzer.weights = make([][]float64, 2)
	analyzer.weights[0] = sap.initializeWeightMatrix(inputDim, hiddenDim)
	analyzer.weights[1] = sap.initializeWeightMatrix(hiddenDim, outputDim)

	// 初始化偏置
	analyzer.biases = make([]float64, hiddenDim+outputDim)
	for i := range analyzer.biases {
		analyzer.biases[i] = sap.randomNormal(0, 0.1)
	}

	sap.sentimentAnalyzer = analyzer
	log.Printf("   ✅ 情感分析模型初始化完成: %d->%d->%d", inputDim, hiddenDim, outputDim)
}

// initializeEntityRecognizer 初始化实体识别模型
func (sap *SpagoAdvancedProcessor) initializeEntityRecognizer() {
	log.Printf("   🔧 初始化实体识别模型...")

	inputDim := 128 // 词向量维度
	outputDim := 8  // 实体类别数：PERSON, ORG, LOC, MISC, TIME, NUM, O, OTHER

	recognizer := &EntityRecognitionModel{
		inputDim:    inputDim,
		outputDim:   outputDim,
		entityTypes: []string{"O", "PERSON", "ORG", "LOC", "MISC", "TIME", "NUM", "OTHER"},
	}

	// 初始化权重矩阵（单层分类器）
	recognizer.weights = make([][]float64, 1)
	recognizer.weights[0] = sap.initializeWeightMatrix(inputDim, outputDim)

	// 初始化偏置
	recognizer.biases = make([]float64, outputDim)
	for i := range recognizer.biases {
		recognizer.biases[i] = sap.randomNormal(0, 0.1)
	}

	sap.entityRecognizer = recognizer
	log.Printf("   ✅ 实体识别模型初始化完成: %d->%d", inputDim, outputDim)
}

// preprocessAndTokenize 预处理和分词
func (sap *SpagoAdvancedProcessor) preprocessAndTokenize(text string) []string {
	// 1. 文本清理
	cleanText := strings.TrimSpace(text)
	cleanText = strings.ReplaceAll(cleanText, "\n", " ")
	cleanText = strings.ReplaceAll(cleanText, "\t", " ")

	// 2. 简单分词（可以集成更复杂的中文分词器）
	tokens := strings.Fields(cleanText)

	// 3. 过滤和标准化
	cleanTokens := []string{}
	for _, token := range tokens {
		token = strings.TrimSpace(token)
		if len(token) >= 1 && len(token) <= 50 {
			cleanTokens = append(cleanTokens, token)
		}
	}

	return cleanTokens
}

// generateTokenEmbeddings 生成词向量
func (sap *SpagoAdvancedProcessor) generateTokenEmbeddings(tokens []string) [][]float64 {
	embeddings := [][]float64{}

	for _, token := range tokens {
		var embedding []float64

		// 优先使用预训练词向量
		if pretrainedEmb, exists := sap.embeddings[token]; exists {
			embedding = make([]float64, len(pretrainedEmb))
			copy(embedding, pretrainedEmb)
		} else {
			// 为未知词生成词向量
			embedding = sap.generateUnknownTokenEmbedding(token)
		}

		embeddings = append(embeddings, embedding)
	}

	return embeddings
}

// generateSentenceEmbedding 生成句子向量
func (sap *SpagoAdvancedProcessor) generateSentenceEmbedding(tokenEmbeddings [][]float64) []float64 {
	if len(tokenEmbeddings) == 0 {
		return make([]float64, 128) // 返回零向量
	}

	embeddingDim := len(tokenEmbeddings[0])
	sentenceEmbedding := make([]float64, embeddingDim)

	// 使用加权平均方法生成句子向量
	totalWeight := 0.0
	for i, tokenEmb := range tokenEmbeddings {
		// 计算词汇权重（位置权重 + 长度权重）
		positionWeight := 1.0 / (1.0 + float64(i)*0.1)                // 前面的词权重更高
		lengthWeight := 1.0 + math.Log(float64(len(tokenEmbeddings))) // 长句子权重调整
		weight := positionWeight * lengthWeight

		for j, val := range tokenEmb {
			sentenceEmbedding[j] += val * weight
		}
		totalWeight += weight
	}

	// 归一化
	if totalWeight > 0 {
		for i := range sentenceEmbedding {
			sentenceEmbedding[i] /= totalWeight
		}
	}

	return sentenceEmbedding
}

// classifyText 文本分类
func (sap *SpagoAdvancedProcessor) classifyText(sentenceEmbedding []float64) []ClassificationInfo {
	if sap.textClassifier == nil {
		return []ClassificationInfo{}
	}

	// 前向传播
	hidden := sap.forwardLayer(sentenceEmbedding, sap.textClassifier.weights[0],
		sap.textClassifier.biases[:sap.textClassifier.hiddenDim])
	hidden = sap.applyReLU(hidden)

	output := sap.forwardLayer(hidden, sap.textClassifier.weights[1],
		sap.textClassifier.biases[sap.textClassifier.hiddenDim:])
	probabilities := sap.applySoftmax(output)

	// 构建分类结果
	classifications := []ClassificationInfo{}
	for i, prob := range probabilities {
		if i < len(sap.categories) {
			classifications = append(classifications, ClassificationInfo{
				Label:      sap.categories[i],
				Score:      prob,
				Confidence: prob,
			})
		}
	}

	// 按概率排序
	sort.Slice(classifications, func(i, j int) bool {
		return classifications[i].Score > classifications[j].Score
	})

	return classifications
}

// analyzeSentiment 情感分析
func (sap *SpagoAdvancedProcessor) analyzeSentiment(sentenceEmbedding []float64) (float64, string, []float64) {
	if sap.sentimentAnalyzer == nil {
		return 0.5, "中性", []float64{0.33, 0.34, 0.33}
	}

	// 前向传播
	hidden := sap.forwardLayer(sentenceEmbedding, sap.sentimentAnalyzer.weights[0],
		sap.sentimentAnalyzer.biases[:sap.sentimentAnalyzer.hiddenDim])
	hidden = sap.applyReLU(hidden)

	output := sap.forwardLayer(hidden, sap.sentimentAnalyzer.weights[1],
		sap.sentimentAnalyzer.biases[sap.sentimentAnalyzer.hiddenDim:])
	probabilities := sap.applySoftmax(output)

	// 找到最高概率的类别
	maxIdx := 0
	maxProb := probabilities[0]
	for i, prob := range probabilities {
		if prob > maxProb {
			maxProb = prob
			maxIdx = i
		}
	}

	// 计算情感分数（0-1范围）
	sentimentScore := probabilities[2] // 积极情感的概率
	sentimentLabel := sap.sentimentAnalyzer.sentimentMap[maxIdx]

	return sentimentScore, sentimentLabel, probabilities
}

// recognizeEntities 实体识别
func (sap *SpagoAdvancedProcessor) recognizeEntities(tokens []string, tokenEmbeddings [][]float64) []EntityPrediction {
	if sap.entityRecognizer == nil || len(tokens) != len(tokenEmbeddings) {
		return []EntityPrediction{}
	}

	entities := []EntityPrediction{}

	for i, tokenEmb := range tokenEmbeddings {
		// 对每个词进行实体分类
		output := sap.forwardLayer(tokenEmb, sap.entityRecognizer.weights[0], sap.entityRecognizer.biases)
		probabilities := sap.applySoftmax(output)

		// 找到最高概率的实体类型
		maxIdx := 0
		maxProb := probabilities[0]
		for j, prob := range probabilities {
			if prob > maxProb {
				maxProb = prob
				maxIdx = j
			}
		}

		// 如果不是"O"（非实体）且置信度足够高
		if maxIdx > 0 && maxProb > 0.5 {
			entity := EntityPrediction{
				Text:          tokens[i],
				Label:         sap.entityRecognizer.entityTypes[maxIdx],
				Confidence:    maxProb,
				StartPos:      i,
				EndPos:        i + 1,
				Probabilities: probabilities,
			}
			entities = append(entities, entity)
		}
	}

	return entities
}

// extractAdvancedFeatures 提取高级特征
func (sap *SpagoAdvancedProcessor) extractAdvancedFeatures(text string, tokenEmbeddings [][]float64, sentenceEmbedding []float64) map[string]float64 {
	features := make(map[string]float64)

	// 基础统计特征
	features["text_length"] = float64(len(text))
	features["token_count"] = float64(len(tokenEmbeddings))
	features["avg_token_length"] = sap.calculateAverageTokenLength(text)

	// 词向量特征
	if len(tokenEmbeddings) > 0 {
		features["embedding_variance"] = sap.calculateEmbeddingVariance(tokenEmbeddings)
		features["embedding_norm"] = sap.calculateVectorNorm(sentenceEmbedding)
		features["embedding_sparsity"] = sap.calculateSparsity(sentenceEmbedding)
	}

	// 语言特征
	features["chinese_ratio"] = sap.calculateChineseRatio(text)
	features["digit_ratio"] = sap.calculateDigitRatio(text)
	features["punctuation_ratio"] = sap.calculatePunctuationRatio(text)

	// 词汇特征
	features["known_word_ratio"] = sap.calculateKnownWordRatio(tokenEmbeddings)
	features["vocabulary_diversity"] = sap.calculateVocabularyDiversity(text)

	// 语义特征
	features["semantic_density"] = sap.calculateSemanticDensity(tokenEmbeddings)
	features["coherence_score"] = sap.calculateCoherenceScore(tokenEmbeddings)

	return features
}

// calculateOverallConfidence 计算综合置信度
func (sap *SpagoAdvancedProcessor) calculateOverallConfidence(result *SpagoResult) float64 {
	confidence := 0.0

	// 分类置信度
	if len(result.Classifications) > 0 {
		confidence += result.Classifications[0].Confidence * 0.3
	}

	// 情感分析置信度
	if len(result.SentimentProbs) > 0 {
		maxSentimentProb := 0.0
		for _, prob := range result.SentimentProbs {
			if prob > maxSentimentProb {
				maxSentimentProb = prob
			}
		}
		confidence += maxSentimentProb * 0.2
	}

	// 实体识别置信度
	if len(result.Entities) > 0 {
		avgEntityConfidence := 0.0
		for _, entity := range result.Entities {
			avgEntityConfidence += entity.Confidence
		}
		avgEntityConfidence /= float64(len(result.Entities))
		confidence += avgEntityConfidence * 0.2
	}

	// 特征质量置信度
	if embeddingNorm, exists := result.Features["embedding_norm"]; exists {
		normalizedNorm := math.Min(embeddingNorm/10.0, 1.0) // 归一化
		confidence += normalizedNorm * 0.15
	}

	// 文本质量置信度
	if chineseRatio, exists := result.Features["chinese_ratio"]; exists {
		confidence += chineseRatio * 0.15
	}

	return math.Min(confidence, 1.0)
}
