package main

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 尝试不同的连接方式
	connectionStrings := []string{
		"root:park%25123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local",
		"root:park%123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local",
		"root:park%2523456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local",
	}

	var db *sql.DB
	var err error

	for i, connStr := range connectionStrings {
		fmt.Printf("🔗 尝试连接方式 %d: %s\n", i+1, connStr)
		db, err = sql.Open("mysql", connStr)
		if err != nil {
			fmt.Printf("❌ 连接失败: %v\n", err)
			continue
		}

		// 测试连接
		err = db.Ping()
		if err != nil {
			fmt.Printf("❌ Ping失败: %v\n", err)
			db.Close()
			continue
		}

		fmt.Printf("✅ 连接成功！\n")
		break
	}

	if err != nil {
		log.Fatalf("所有连接方式都失败了")
	}
	defer db.Close()

	fmt.Println("🔍 测试搜索修复效果...")

	// 先检查数据库中有多少数据
	fmt.Println("\n📊 检查数据库状态...")
	checkDatabaseStatus(db)

	// 测试应用层搜索
	fmt.Println("\n🔍 测试应用层搜索...")
	testApplicationSearch(db, "一言百宝箱")
}

func checkDatabaseStatus(db *sql.DB) {
	// 检查总记录数
	var totalCount int
	err := db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&totalCount)
	if err != nil {
		fmt.Printf("❌ 查询总记录数失败: %v\n", err)
		return
	}
	fmt.Printf("📊 数据库中总共有 %d 条记录\n", totalCount)

	// 检查已批准的记录数
	var approvedCount int
	err = db.QueryRow("SELECT COUNT(*) FROM learned_knowledge WHERE status = 'approved'").Scan(&approvedCount)
	if err != nil {
		fmt.Printf("❌ 查询已批准记录数失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 其中已批准的记录: %d 条\n", approvedCount)

	// 检查待审核的记录数
	var pendingCount int
	err = db.QueryRow("SELECT COUNT(*) FROM learned_knowledge WHERE status = 'pending'").Scan(&pendingCount)
	if err != nil {
		fmt.Printf("❌ 查询待审核记录数失败: %v\n", err)
		return
	}
	fmt.Printf("⏳ 待审核的记录: %d 条\n", pendingCount)

	// 显示最近的几条记录
	fmt.Println("\n📝 最近的几条记录:")
	rows, err := db.Query(`
		SELECT id, question, answer, status, created_at
		FROM learned_knowledge
		ORDER BY created_at DESC
		LIMIT 5
	`)
	if err != nil {
		fmt.Printf("❌ 查询最近记录失败: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var id int
		var question, answer, status, createdAt string
		err := rows.Scan(&id, &question, &answer, &status, &createdAt)
		if err != nil {
			continue
		}

		// 截断长文本
		if len(question) > 30 {
			question = question[:30] + "..."
		}
		if len(answer) > 40 {
			answer = answer[:40] + "..."
		}

		fmt.Printf("  ID %d [%s]: %s -> %s\n", id, status, question, answer)
	}
}

func testApplicationSearch(db *sql.DB, searchTerm string) {
	fmt.Printf("\n搜索词: '%s'\n", searchTerm)

	// 获取所有知识进行应用层过滤
	query := `
		SELECT id, question, answer, confidence
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY confidence DESC, created_at DESC
		LIMIT 50
	`

	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	searchLower := strings.ToLower(searchTerm)
	found := false
	matchCount := 0

	for rows.Next() {
		var id int
		var question, answer string
		var confidence float32

		err := rows.Scan(&id, &question, &answer, &confidence)
		if err != nil {
			continue
		}

		questionLower := strings.ToLower(question)
		answerLower := strings.ToLower(answer)

		if strings.Contains(questionLower, searchLower) || strings.Contains(answerLower, searchLower) {
			if !found {
				fmt.Println("找到的结果:")
				found = true
			}

			matchCount++

			// 截断长文本
			if len(question) > 40 {
				question = question[:40] + "..."
			}
			if len(answer) > 60 {
				answer = answer[:60] + "..."
			}

			fmt.Printf("  ID %d (置信度 %.2f): %s -> %s\n", id, confidence, question, answer)
		}
	}

	if !found {
		fmt.Println("❌ 未找到匹配结果")
	} else {
		fmt.Printf("✅ 找到 %d 个匹配结果\n", matchCount)
	}
}
