<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>爬虫管理仪表板 - FAQ系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .dashboard {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #4facfe;
        }

        .stat-card h3 {
            color: #333;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .stat-card p {
            color: #666;
            font-size: 1.1em;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .targets-section {
            margin-top: 30px;
        }

        .section-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }

        .targets-grid {
            display: grid;
            gap: 20px;
        }

        .target-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #28a745;
        }

        .target-card.disabled {
            border-left-color: #dc3545;
            opacity: 0.7;
        }

        .target-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .target-name {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
        }

        .target-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .target-info {
            color: #666;
            margin-bottom: 10px;
        }

        .target-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.9em;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        /* 爬取中的动画效果 */
        @keyframes crawling {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }

        .target-card.crawling {
            animation: crawling 2s infinite;
            border-left-color: #ffc107 !important;
        }

        .status-crawling {
            background: #fff3cd;
            color: #856404;
            animation: crawling 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕷️ 爬虫管理仪表板</h1>
            <p>智能知识爬取与管理系统</p>
        </div>

        <div class="dashboard">
            <!-- 统计信息 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalTargets">-</h3>
                    <p>爬取目标</p>
                </div>
                <div class="stat-card">
                    <h3 id="activeTargets">-</h3>
                    <p>活跃目标</p>
                </div>
                <div class="stat-card">
                    <h3 id="crawlingNow">-</h3>
                    <p>正在爬取</p>
                </div>
                <div class="stat-card">
                    <h3 id="successRate">-</h3>
                    <p>成功率</p>
                </div>
            </div>

            <!-- 控制按钮 -->
            <div class="controls">
                <button class="btn btn-success" onclick="startCrawler()">🚀 启动爬虫</button>
                <button class="btn btn-danger" onclick="stopCrawler()">⏹️ 停止爬虫</button>
                <button class="btn btn-primary" onclick="refreshData()">🔄 刷新数据</button>
                <button class="btn btn-primary" onclick="showAddTargetForm()">➕ 添加目标</button>
            </div>

            <!-- 消息显示区域 -->
            <div id="messageArea"></div>

            <!-- 正在爬取的目标 -->
            <div class="targets-section">
                <h2 class="section-title">🕷️ 正在爬取</h2>
                <div id="activeCrawlsContainer" class="loading">
                    正在加载活跃爬取...
                </div>
            </div>

            <!-- 爬取目标列表 -->
            <div class="targets-section">
                <h2 class="section-title">📋 爬取目标</h2>
                <div id="targetsContainer" class="loading">
                    正在加载爬取目标...
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let crawlerStatus = { running: false, total_targets: 0, active_targets: 0, crawling_now: 0, active_crawls: [] };
        let targets = [];
        let activeCrawls = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            // 每30秒自动刷新一次
            setInterval(refreshData, 30000);
        });

        // 刷新数据
        async function refreshData() {
            try {
                await Promise.all([
                    loadCrawlerStatus(),
                    loadTargets(),
                    loadStatistics()
                ]);
            } catch (error) {
                showMessage('加载数据失败: ' + error.message, 'error');
            }
        }

        // 加载爬虫状态
        async function loadCrawlerStatus() {
            try {
                const response = await fetch('/api/crawler/status');
                if (response.ok) {
                    const data = await response.json();
                    crawlerStatus = data.data || {};
                    activeCrawls = crawlerStatus.active_crawls || [];
                    renderActiveCrawls();
                }
            } catch (error) {
                console.error('加载爬虫状态失败:', error);
            }
        }

        // 加载爬取目标
        async function loadTargets() {
            try {
                const response = await fetch('/api/crawler/targets');
                if (response.ok) {
                    const data = await response.json();
                    targets = data.data || [];
                    renderTargets();
                } else {
                    document.getElementById('targetsContainer').innerHTML = 
                        '<div class="error">无法加载爬取目标，请检查爬虫服务是否正常运行</div>';
                }
            } catch (error) {
                document.getElementById('targetsContainer').innerHTML = 
                    '<div class="error">连接爬虫服务失败，API可能尚未启用</div>';
            }
        }

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch('/api/crawler/statistics');
                if (response.ok) {
                    const data = await response.json();
                    updateStatistics(data.data);
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
                // 使用默认值
                updateStatistics({
                    total_targets: targets.length,
                    active_targets: targets.filter(t => t.enabled).length,
                    total_crawls: 0,
                    successful_crawls: 0
                });
            }
        }

        // 更新统计信息显示
        function updateStatistics(stats) {
            document.getElementById('totalTargets').textContent = crawlerStatus.total_targets || stats.total_targets || 0;
            document.getElementById('activeTargets').textContent = crawlerStatus.active_targets || stats.active_targets || 0;
            document.getElementById('crawlingNow').textContent = crawlerStatus.crawling_now || 0;

            const successRate = stats.total_crawls > 0
                ? Math.round((stats.successful_crawls / stats.total_crawls) * 100)
                : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        // 渲染爬取目标
        function renderTargets() {
            const container = document.getElementById('targetsContainer');

            if (targets.length === 0) {
                container.innerHTML = '<div class="loading">暂无爬取目标</div>';
                return;
            }

            const html = targets.map(target => {
                // 检查是否正在爬取中
                const isCrawling = activeCrawls.some(active => active.id === target.id);
                const crawlingIndicator = isCrawling ? '🕷️ ' : '';
                const crawlingClass = isCrawling ? 'crawling' : '';
                const crawlingStatus = isCrawling ? '<div class="target-status status-crawling">爬取中</div>' : '';

                return `
                <div class="target-card ${target.enabled ? '' : 'disabled'} ${crawlingClass}">
                    <div class="target-header">
                        <div class="target-name">${crawlingIndicator}${target.name}</div>
                        ${crawlingStatus || `<div class="target-status ${target.enabled ? 'status-active' : 'status-inactive'}">
                            ${target.enabled ? '启用' : '禁用'}
                        </div>`}
                    </div>
                    <div class="target-info">
                        <div><strong>URL:</strong> ${target.url}</div>
                        <div><strong>类型:</strong> ${target.type}</div>
                        <div><strong>分类:</strong> ${target.category}</div>
                        <div><strong>调度:</strong> ${getScheduleText(target.schedule)}</div>
                        ${target.last_crawled ? `<div><strong>最后爬取:</strong> ${new Date(target.last_crawled).toLocaleString()}</div>` : ''}
                    </div>
                    <div class="target-actions">
                        <button class="btn btn-primary btn-sm" onclick="manualCrawl(${target.id})" ${isCrawling ? 'disabled' : ''}>
                            ${isCrawling ? '爬取中...' : '手动爬取'}
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="editTarget(${target.id})" ${isCrawling ? 'disabled' : ''}>✏️ 编辑</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteTarget(${target.id})" ${isCrawling ? 'disabled' : ''}>🗑️ 删除</button>
                        <button class="btn btn-secondary btn-sm" onclick="toggleTarget(${target.id}, ${!target.enabled})" ${isCrawling ? 'disabled' : ''}>
                            ${target.enabled ? '🔴 禁用' : '🟢 启用'}
                        </button>
                    </div>
                </div>
            `;
            }).join('');

            container.innerHTML = html;
        }

        // 获取调度文本描述
        function getScheduleText(schedule) {
            const scheduleMap = {
                '* * * * *': '每分钟',
                '0 */1 * * *': '每小时',
                '0 */6 * * *': '每6小时',
                '0 0 * * *': '每天',
                '0 0 * * 0': '每周'
            };
            return scheduleMap[schedule] || schedule;
        }

        // 启动爬虫
        async function startCrawler() {
            try {
                const response = await fetch('/api/crawler/start', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    showMessage('爬虫启动成功', 'success');
                    refreshData();
                } else {
                    showMessage('启动失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('启动爬虫失败: 爬虫API尚未启用', 'error');
            }
        }

        // 停止爬虫
        async function stopCrawler() {
            try {
                const response = await fetch('/api/crawler/stop', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    showMessage('爬虫停止成功', 'success');
                    refreshData();
                } else {
                    showMessage('停止失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('停止爬虫失败: 爬虫API尚未启用', 'error');
            }
        }

        // 手动爬取
        async function manualCrawl(targetId) {
            try {
                const response = await fetch(`/api/crawler/targets/${targetId}/crawl`, { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage('🚀 手动爬取任务已启动，正在执行...', 'success');

                    // 立即刷新状态以显示正在爬取的目标
                    setTimeout(() => {
                        refreshData();
                    }, 500); // 500ms后刷新，给爬虫一点时间启动

                    // 再次刷新以更新最终状态
                    setTimeout(() => {
                        refreshData();
                    }, 3000); // 3秒后再次刷新
                } else {
                    showMessage('启动失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('手动爬取失败: ' + error.message, 'error');
            }
        }

        // 删除目标
        async function deleteTarget(targetId) {
            if (!confirm('确定要删除这个爬取目标吗？')) {
                return;
            }

            try {
                const response = await fetch(`/api/crawler/targets/${targetId}`, { method: 'DELETE' });
                const data = await response.json();

                if (data.success) {
                    showMessage('目标删除成功', 'success');
                    refreshData();
                } else {
                    showMessage('删除失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('删除目标失败: 爬虫API尚未启用', 'error');
            }
        }

        // 编辑目标
        async function editTarget(targetId) {
            try {
                // 获取目标详情
                const response = await fetch(`/api/crawler/targets/${targetId}`);
                const data = await response.json();

                if (!data.success) {
                    showMessage('获取目标信息失败: ' + data.message, 'error');
                    return;
                }

                const target = data.target;
                showEditTargetForm(target);
            } catch (error) {
                showMessage('获取目标信息失败: ' + error.message, 'error');
            }
        }

        // 启用/禁用目标
        async function toggleTarget(targetId, enable) {
            try {
                const action = enable ? 'enable' : 'disable';
                const response = await fetch(`/api/crawler/targets/${targetId}/${action}`, { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage(`目标${enable ? '启用' : '禁用'}成功`, 'success');
                    refreshData();
                } else {
                    showMessage(`${enable ? '启用' : '禁用'}失败: ` + data.message, 'error');
                }
            } catch (error) {
                showMessage(`${enable ? '启用' : '禁用'}目标失败: ` + error.message, 'error');
            }
        }

        // 渲染正在爬取的目标
        function renderActiveCrawls() {
            const container = document.getElementById('activeCrawlsContainer');

            if (activeCrawls.length === 0) {
                container.innerHTML = '<div class="loading">暂无正在爬取的目标</div>';
                return;
            }

            const html = activeCrawls.map(target => `
                <div class="target-card">
                    <div class="target-header">
                        <div class="target-name">🕷️ ${target.name}</div>
                        <div class="target-status status-active">爬取中</div>
                    </div>
                    <div class="target-info">
                        <div><strong>URL:</strong> ${target.url}</div>
                        <div><strong>类型:</strong> ${target.type}</div>
                        <div><strong>分类:</strong> ${target.category}</div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 显示添加目标表单
        function showAddTargetForm() {
            const formHtml = `
                <div id="addTargetModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 30px; border-radius: 10px; width: 90%; max-width: 600px; max-height: 80%; overflow-y: auto;">
                        <h3 style="margin-bottom: 20px;">➕ 添加爬取目标</h3>
                        <form id="addTargetForm">
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">目标名称:</label>
                                <input type="text" id="targetName" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">目标URL:</label>
                                <input type="url" id="targetUrl" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">类型:</label>
                                <select id="targetType" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="website">网站</option>
                                    <option value="api">API</option>
                                    <option value="rss">RSS</option>
                                    <option value="search_engine">搜索引擎</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">分类:</label>
                                <select id="targetCategory" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="technology">技术</option>
                                    <option value="programming">编程</option>
                                    <option value="database">数据库</option>
                                    <option value="devops">运维</option>
                                    <option value="ai">人工智能</option>
                                    <option value="general">通用</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">关键词 (用逗号分隔):</label>
                                <input type="text" id="targetKeywords" placeholder="例如: python, programming, tutorial" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">调度频率:</label>
                                <select id="targetSchedule" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="* * * * *">每分钟</option>
                                    <option value="0 */1 * * *">每小时</option>
                                    <option value="0 */6 * * *" selected>每6小时</option>
                                    <option value="0 0 * * *">每天</option>
                                    <option value="0 0 * * 0">每周</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 20px;">
                                <label style="display: flex; align-items: center;">
                                    <input type="checkbox" id="targetEnabled" checked style="margin-right: 8px;">
                                    启用此目标
                                </label>
                            </div>
                            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                                <button type="button" onclick="closeAddTargetForm()" class="btn" style="background: #6c757d; color: white;">取消</button>
                                <button type="submit" class="btn btn-primary">添加目标</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', formHtml);

            // 绑定表单提交事件
            document.getElementById('addTargetForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                await submitAddTargetForm();
            });
        }

        // 关闭添加目标表单
        function closeAddTargetForm() {
            const modal = document.getElementById('addTargetModal');
            if (modal) {
                modal.remove();
            }
        }

        // 提交添加目标表单
        async function submitAddTargetForm() {
            const name = document.getElementById('targetName').value;
            const url = document.getElementById('targetUrl').value;
            const type = document.getElementById('targetType').value;
            const category = document.getElementById('targetCategory').value;
            const keywordsStr = document.getElementById('targetKeywords').value;
            const schedule = document.getElementById('targetSchedule').value;
            const enabled = document.getElementById('targetEnabled').checked;

            const keywords = keywordsStr.split(',').map(k => k.trim()).filter(k => k);

            const targetData = {
                name,
                url,
                type,
                category,
                keywords,
                schedule,
                enabled,
                selectors: {
                    title: "title, h1, .title",
                    content: ".content, .post-content, .entry-content, article, main"
                },
                filters: {
                    min_content_length: 100
                }
            };

            try {
                const response = await fetch('/api/crawler/targets', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(targetData)
                });

                const data = await response.json();

                if (data.success) {
                    showMessage('爬取目标添加成功', 'success');
                    closeAddTargetForm();
                    refreshData();
                } else {
                    showMessage('添加失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('添加目标失败: ' + error.message, 'error');
            }
        }

        // 显示消息
        function showMessage(message, type) {
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            
            messageArea.innerHTML = '';
            messageArea.appendChild(messageDiv);
            
            // 3秒后自动隐藏
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // 显示编辑目标表单
        function showEditTargetForm(target) {
            const formHtml = `
                <div id="editTargetModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 30px; border-radius: 10px; width: 90%; max-width: 600px; max-height: 80%; overflow-y: auto;">
                        <h3 style="margin-bottom: 20px;">✏️ 编辑爬取目标</h3>
                        <form id="editTargetForm">
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">目标名称:</label>
                                <input type="text" id="editTargetName" value="${target.name}" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">目标URL:</label>
                                <input type="url" id="editTargetUrl" value="${target.url}" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">类型:</label>
                                <select id="editTargetType" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="website" ${target.type === 'website' ? 'selected' : ''}>网站</option>
                                    <option value="blog" ${target.type === 'blog' ? 'selected' : ''}>博客</option>
                                    <option value="news" ${target.type === 'news' ? 'selected' : ''}>新闻</option>
                                    <option value="forum" ${target.type === 'forum' ? 'selected' : ''}>论坛</option>
                                    <option value="api" ${target.type === 'api' ? 'selected' : ''}>API</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">分类:</label>
                                <input type="text" id="editTargetCategory" value="${target.category}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">关键词 (用逗号分隔):</label>
                                <input type="text" id="editTargetKeywords" value="${target.keywords ? target.keywords.join(', ') : ''}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">调度:</label>
                                <select id="editTargetSchedule" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="manual" ${target.schedule === 'manual' ? 'selected' : ''}>手动</option>
                                    <option value="hourly" ${target.schedule === 'hourly' ? 'selected' : ''}>每小时</option>
                                    <option value="daily" ${target.schedule === 'daily' ? 'selected' : ''}>每天</option>
                                    <option value="weekly" ${target.schedule === 'weekly' ? 'selected' : ''}>每周</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 20px;">
                                <label style="display: flex; align-items: center;">
                                    <input type="checkbox" id="editTargetEnabled" ${target.enabled ? 'checked' : ''} style="margin-right: 8px;">
                                    启用此目标
                                </label>
                            </div>
                            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                                <button type="button" onclick="closeEditTargetForm()" class="btn" style="background: #6c757d; color: white;">取消</button>
                                <button type="submit" class="btn btn-primary">保存修改</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', formHtml);

            // 绑定表单提交事件
            document.getElementById('editTargetForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await submitEditTargetForm(target.id);
            });
        }

        // 关闭编辑目标表单
        function closeEditTargetForm() {
            const modal = document.getElementById('editTargetModal');
            if (modal) {
                modal.remove();
            }
        }

        // 提交编辑目标表单
        async function submitEditTargetForm(targetId) {
            const name = document.getElementById('editTargetName').value;
            const url = document.getElementById('editTargetUrl').value;
            const type = document.getElementById('editTargetType').value;
            const category = document.getElementById('editTargetCategory').value;
            const keywordsStr = document.getElementById('editTargetKeywords').value;
            const schedule = document.getElementById('editTargetSchedule').value;
            const enabled = document.getElementById('editTargetEnabled').checked;

            const keywords = keywordsStr.split(',').map(k => k.trim()).filter(k => k);

            const targetData = {
                id: targetId,
                name,
                url,
                type,
                category,
                keywords,
                schedule,
                enabled,
                selectors: {
                    title: "title, h1, .title",
                    content: ".content, .post-content, .entry-content, article, main"
                },
                filters: {
                    min_content_length: 100
                }
            };

            try {
                const response = await fetch(`/api/crawler/targets/${targetId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(targetData)
                });

                const data = await response.json();

                if (data.success) {
                    showMessage('目标更新成功', 'success');
                    closeEditTargetForm();
                    refreshData();
                } else {
                    showMessage('更新失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('更新目标失败: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
