package templates

// GetIndexHTML 返回主页HTML模板
func GetIndexHTML() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能FAQ问答系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            margin-top: 15px;
            font-size: 0.9em;
        }
        .status.healthy { background: rgba(255,255,255,0.2); }
        .status.unhealthy { background: rgba(255,0,0,0.2); }

        .chat-container {
            height: 67vh;
            display: flex;
            flex-direction: column;
        }
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }
        .message.user { justify-content: flex-end; }
        .message.bot { justify-content: flex-start; }
        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
        }
        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .message.bot .message-content {
            background: white;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
        }
        .message.user .message-avatar { background: #667eea; color: white; }
        .message.bot .message-avatar { background: #4facfe; color: white; }
        .message-time {
            font-size: 0.8em;
            opacity: 0.7;
            margin-top: 5px;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        .input-group {
            display: flex;
            gap: 10px;
        }
        .input-field {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        .input-field:focus { border-color: #667eea; }
        .send-btn {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .send-btn:hover { transform: translateY(-2px); }
        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .quick-questions {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        .quick-questions h3 {
            margin-bottom: 15px;
            color: #495057;
        }
        .question-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .question-tag {
            padding: 8px 16px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }
        .question-tag:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .typing-indicator {
            display: none;
            padding: 15px 20px;
            background: white;
            border-radius: 20px;
            border: 1px solid #e9ecef;
            max-width: 70%;
        }
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }
        .welcome-message h2 {
            margin-bottom: 10px;
            color: #495057;
        }

        @media (max-width: 768px) {
            .container { margin: 10px; border-radius: 15px; }
            .header { padding: 20px; }
            .header h1 { font-size: 2em; }
            .message-content { max-width: 85%; }
            .input-group { flex-direction: column; }
            .send-btn { align-self: flex-end; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 智能FAQ问答系统</h1>
            <div id="systemStatus" class="status">检查系统状态中...</div>
            <div id="ragStatus" class="status" style="margin-top: 10px;">📚 智能问答模式</div>
        </div>

        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <h2>🎯 欢迎使用智能问答系统</h2>
                    <p>您可以询问关于LocalAI、MySQL、向量搜索等相关问题</p>
                </div>
            </div>

            <div class="quick-questions">
                <h3>💡 快速提问</h3>
                <div class="question-tags">
                    <div class="question-tag" onclick="askQuestion('什么是LocalAI？')">什么是LocalAI？</div>
                    <div class="question-tag" onclick="askQuestion('如何使用MySQL存储FAQ？')">如何使用MySQL？</div>
                    <div class="question-tag" onclick="askQuestion('向量搜索的原理是什么？')">向量搜索原理</div>
                    <div class="question-tag" onclick="askQuestion('Go语言有什么优势？')">Go语言优势</div>
                    <div class="question-tag" onclick="askQuestion('什么是向量数据库？')">向量数据库</div>
                    <div class="question-tag" onclick="askQuestion('如何部署FAQ系统？')">部署FAQ系统</div>
                </div>
            </div>

            <div class="chat-input">
                <div class="input-group">
                    <input type="text" id="questionInput" class="input-field"
                           placeholder="请输入您的问题..."
                           onkeypress="handleKeyPress(event)">
                    <button id="sendBtn" class="send-btn" onclick="sendQuestion()">发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 内嵌JavaScript以避免静态文件服务
        let isWaiting = false;

        async function checkSystemStatus() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                const statusEl = document.getElementById('systemStatus');
                if (data.healthy) {
                    statusEl.textContent = '🟢 FAQ系统正常';
                    statusEl.className = 'status healthy';
                } else {
                    statusEl.textContent = '🟡 FAQ系统部分异常';
                    statusEl.className = 'status healthy';
                }
            } catch (error) {
                const statusEl = document.getElementById('systemStatus');
                statusEl.textContent = '🔴 FAQ系统连接失败';
                statusEl.className = 'status unhealthy';
            }
        }

        function addMessage(content, isUser = false, time = null) {
            const messagesContainer = document.getElementById('chatMessages');
            const welcomeMsg = messagesContainer.querySelector('.welcome-message');
            if (welcomeMsg) welcomeMsg.remove();

            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + (isUser ? 'user' : 'bot');
            const currentTime = time || new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit', minute: '2-digit'
            });

            messageDiv.innerHTML = ` + "`" + `
                <div class="message-avatar">${isUser ? '👤' : '🤖'}</div>
                <div class="message-content">
                    ${content}
                    <div class="message-time">${currentTime}</div>
                </div>
            ` + "`" + `;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showTypingIndicator() {
            const messagesContainer = document.getElementById('chatMessages');
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message bot';
            typingDiv.id = 'typingIndicator';
            typingDiv.innerHTML = ` + "`" + `
                <div class="message-avatar">🤖</div>
                <div class="typing-indicator" style="display: block;">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            ` + "`" + `;
            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) typingIndicator.remove();
        }

        async function sendQuestion() {
            if (isWaiting) return;
            const input = document.getElementById('questionInput');
            const question = input.value.trim();
            if (!question) { alert('请输入问题'); return; }

            addMessage(question, true);
            input.value = '';
            isWaiting = true;
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';
            showTypingIndicator();

            try {
                const response = await fetch('/ask', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ question: question })
                });
                const data = await response.json();
                hideTypingIndicator();

                if (response.ok) {
                    const answerWithInfo = data.answer.replace(/\n/g, '<br>') +
                        '<br><small style="opacity: 0.7;">🤖 ' + data.source + ' • ⏱️ ' + data.duration + '</small>';
                    addMessage(answerWithInfo, false);
                } else {
                    addMessage('❌ 抱歉，处理您的问题时出现错误：' + (data.error || '未知错误'), false);
                }
            } catch (error) {
                hideTypingIndicator();
                addMessage('❌ 网络连接错误，请检查服务是否正常运行', false);
            } finally {
                isWaiting = false;
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
                input.focus();
            }
        }

        function askQuestion(question) {
            document.getElementById('questionInput').value = question;
            sendQuestion();
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendQuestion();
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            document.getElementById('questionInput').focus();
            setInterval(checkSystemStatus, 30000);
        });
    </script>
</body>
</html>`
}

// GetTestHTML 返回测试页面HTML模板
func GetTestHTML() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAQ测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #495057;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-button {
            margin: 5px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            cursor: pointer;
            border-radius: 25px;
            font-size: 14px;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .result {
            margin: 15px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }
        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 FAQ 系统测试页面</h1>
        <div class="status">
            <strong>🎯 RAG + 意图识别测试</strong><br>
            测试不同类型的问题，观察意图识别和回答质量
        </div>

        <div class="test-section">
            <h3>🧠 意图识别测试：</h3>
            <button class="test-button" onclick="testQuestion('你好')">测试：你好 (greeting)</button>
            <button class="test-button" onclick="testQuestion('你是谁')">测试：你是谁 (identity_inquiry)</button>
            <button class="test-button" onclick="testQuestion('什么是LocalAI')">测试：什么是LocalAI (technical_question)</button>
            <button class="test-button" onclick="testQuestion('谢谢')">测试：谢谢 (thanks)</button>
        </div>

        <div class="test-section">
            <h3>🔧 技术问题测试：</h3>
            <button class="test-button" onclick="testQuestion('向量搜索原理')">测试：向量搜索原理</button>
            <button class="test-button" onclick="testQuestion('Go语言优势')">测试：Go语言优势</button>
            <button class="test-button" onclick="testQuestion('MySQL数据库')">测试：MySQL数据库</button>
            <button class="test-button" onclick="testQuestion('什么是向量数据库')">测试：向量数据库</button>
        </div>

        <div class="test-section">
            <h3>💬 上下文对话测试：</h3>
            <button class="test-button" onclick="testSequence()">测试：上下文对话序列</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function testQuestion(question) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<strong>❓ 问题：</strong>' + question + '<br><strong>🤖 回答：</strong>处理中...';
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;

            fetch('/ask', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ question: question })
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.innerHTML = '<strong>❓ 问题：</strong>' + question +
                    '<br><strong>🤖 回答：</strong>' + data.answer.replace(/\n/g, '<br>') +
                    '<br><strong>📊 来源：</strong>' + data.source + ' • ⏱️ ' + data.duration;
            })
            .catch(error => {
                resultDiv.innerHTML = '<strong>❓ 问题：</strong>' + question +
                    '<br><strong>❌ 错误：</strong>' + error;
            });
        }

        function testSequence() {
            const questions = ['什么是LocalAI', '如何安装', '还有其他功能吗'];
            questions.forEach((q, i) => {
                setTimeout(() => testQuestion(q + ' (上下文测试 ' + (i+1) + ')'), i * 2000);
            });
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>`
}
