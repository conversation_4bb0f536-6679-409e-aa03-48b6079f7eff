package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 直接连接数据库
	db, err := sql.Open("mysql", "root:park%123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 检查表结构...")

	// 检查crawl_targets表结构
	fmt.Println("\n📊 crawl_targets表结构:")
	rows, err := db.Query("DESCRIBE crawl_targets")
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
	} else {
		defer rows.Close()
		fmt.Println("字段名\t\t类型\t\t\t空值\t键\t默认值\t额外")
		fmt.Println("---\t\t---\t\t\t---\t---\t---\t---")
		for rows.Next() {
			var field, fieldType, null, key, defaultVal, extra sql.NullString
			rows.Scan(&field, &fieldType, &null, &key, &defaultVal, &extra)
			
			fmt.Printf("%s\t\t%s\t%s\t%s\t%s\t%s\n", 
				field.String, fieldType.String, null.String, key.String, 
				defaultVal.String, extra.String)
		}
	}

	// 查询crawl_targets表的所有数据
	fmt.Println("\n📋 crawl_targets表数据:")
	dataRows, err := db.Query("SELECT * FROM crawl_targets")
	if err != nil {
		fmt.Printf("❌ 查询数据失败: %v\n", err)
		return
	}
	defer dataRows.Close()

	// 获取列名
	columns, err := dataRows.Columns()
	if err != nil {
		fmt.Printf("❌ 获取列名失败: %v\n", err)
		return
	}

	fmt.Printf("列: %v\n", columns)
	
	// 创建接收数据的切片
	values := make([]interface{}, len(columns))
	valuePtrs := make([]interface{}, len(columns))
	for i := range columns {
		valuePtrs[i] = &values[i]
	}

	for dataRows.Next() {
		err := dataRows.Scan(valuePtrs...)
		if err != nil {
			fmt.Printf("❌ 扫描失败: %v\n", err)
			continue
		}

		fmt.Print("数据: ")
		for i, col := range columns {
			val := values[i]
			if val == nil {
				fmt.Printf("%s=NULL ", col)
			} else {
				switch v := val.(type) {
				case []byte:
					fmt.Printf("%s=%s ", col, string(v))
				default:
					fmt.Printf("%s=%v ", col, v)
				}
			}
		}
		fmt.Println()
	}
}
