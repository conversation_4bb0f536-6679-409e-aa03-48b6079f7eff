package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接数据库
	db, err := sql.Open("mysql", "root:park%123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔧 测试自动批准功能...")

	// 1. 检查当前状态分布
	fmt.Println("\n📊 当前学习知识状态分布:")
	checkStatusDistribution(db)

	// 2. 处理一个新的爬取结果来测试自动批准
	fmt.Println("\n🔄 处理新的爬取结果...")
	processNewCrawlResult(db)

	// 3. 再次检查状态分布
	fmt.Println("\n📊 处理后的状态分布:")
	checkStatusDistribution(db)

	// 4. 测试搜索功能
	fmt.Println("\n🔍 测试搜索功能:")
	testSearch(db, "热搜")
}

func checkStatusDistribution(db *sql.DB) {
	query := `
		SELECT status, COUNT(*) as count
		FROM learned_knowledge
		GROUP BY status
		ORDER BY count DESC
	`

	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询状态分布失败: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var status string
		var count int

		err := rows.Scan(&status, &count)
		if err != nil {
			continue
		}

		fmt.Printf("  %s: %d 条\n", status, count)
	}
}

func processNewCrawlResult(db *sql.DB) {
	// 查找一个未处理的爬取结果
	query := `
		SELECT id, target_id, title, summary, content, url, category, keywords, crawled_at
		FROM crawl_results 
		WHERE target_id = 7 AND status = 'pending'
		ORDER BY crawled_at DESC 
		LIMIT 1
	`
	
	var id, targetID int
	var title, summary, content, url, category, keywords, crawledAt string
	
	err := db.QueryRow(query).Scan(&id, &targetID, &title, &summary, &content, &url, &category, &keywords, &crawledAt)
	if err != nil {
		fmt.Printf("❌ 没有找到未处理的爬取结果: %v\n", err)
		return
	}

	fmt.Printf("📋 找到未处理记录: ID=%d, URL=%s\n", id, url)

	// 模拟知识提取和保存（使用修改后的逻辑）
	// 这里我们直接运行test_fix.go的逻辑
	fmt.Println("🔧 运行知识提取...")
	
	// 由于我们已经修改了爬虫代码，新的知识应该自动设置为approved状态
	// 这里我们可以手动触发一次处理
	
	// 更新爬取结果状态，触发重新处理
	updateQuery := "UPDATE crawl_results SET status = 'pending', processed_at = NULL WHERE id = ?"
	_, err = db.Exec(updateQuery, id)
	if err != nil {
		fmt.Printf("❌ 重置状态失败: %v\n", err)
		return
	}

	fmt.Println("✅ 已重置爬取结果状态，等待系统重新处理...")
	fmt.Println("💡 提示：需要爬虫系统运行才能看到自动批准效果")
}

func testSearch(db *sql.DB, query string) {
	// 测试搜索approved状态的知识
	searchQuery := `
		SELECT id, question, answer, status, confidence
		FROM learned_knowledge
		WHERE status = 'approved'
		AND (question LIKE CONCAT('%', ?, '%') OR answer LIKE CONCAT('%', ?, '%'))
		ORDER BY confidence DESC, created_at DESC
		LIMIT 3
	`

	rows, err := db.Query(searchQuery, query, query)
	if err != nil {
		fmt.Printf("❌ 搜索失败: %v\n", err)
		return
	}
	defer rows.Close()

	found := false
	for rows.Next() {
		var id int
		var question, answer, status string
		var confidence float32

		err := rows.Scan(&id, &question, &answer, &status, &confidence)
		if err != nil {
			continue
		}

		if !found {
			fmt.Printf("✅ 搜索 '%s' 的结果:\n", query)
			found = true
		}

		answerPreview := answer
		if len(answerPreview) > 100 {
			answerPreview = answerPreview[:100] + "..."
		}

		fmt.Printf("  ID %d (状态: %s, 置信度: %.2f): %s\n", id, status, confidence, question)
		fmt.Printf("  答案: %s\n", answerPreview)
	}

	if !found {
		fmt.Printf("❌ 搜索 '%s' 未找到approved状态的结果\n", query)
	}

	// 同时检查pending状态的数量
	var pendingCount int
	countQuery := "SELECT COUNT(*) FROM learned_knowledge WHERE status = 'pending'"
	err = db.QueryRow(countQuery).Scan(&pendingCount)
	if err == nil {
		fmt.Printf("📊 当前pending状态的知识数量: %d\n", pendingCount)
	}
}
