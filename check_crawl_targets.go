package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 直接连接数据库
	db, err := sql.Open("mysql", "root:park%123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 检查爬虫目标配置...")

	// 查询爬虫目标
	query := `
		SELECT id, name, url, category, is_active, created_at
		FROM crawl_targets
		ORDER BY id
	`

	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("ID\t名称\t\t\tURL\t\t\t\t分类\t\t活跃\t创建时间")
	fmt.Println("---\t---\t\t\t---\t\t\t\t---\t\t---\t---")

	for rows.Next() {
		var id int
		var name, url, category, createdAt string
		var isActive bool

		err := rows.Scan(&id, &name, &url, &category, &isActive, &createdAt)
		if err != nil {
			fmt.Printf("❌ 扫描失败: %v\n", err)
			continue
		}

		// 截断长字符串
		if len(name) > 15 {
			name = name[:15] + "..."
		}
		if len(url) > 30 {
			url = url[:30] + "..."
		}

		activeStr := "否"
		if isActive {
			activeStr = "是"
		}

		fmt.Printf("%d\t%s\t%s\t%s\t%s\t%s\n",
			id, name, url, category, activeStr, createdAt[:16])
	}

	// 查询目标ID为7的详细信息（从前面的结果看，百宝箱数据来自目标ID 7）
	fmt.Println("\n🔍 目标ID 7的详细信息:")
	detailQuery := `
		SELECT id, name, url, category, description, is_active
		FROM crawl_targets
		WHERE id = 7
	`

	var id int
	var name, url, category, description string
	var isActive bool

	err = db.QueryRow(detailQuery).Scan(&id, &name, &url, &category, &description, &isActive)
	if err != nil {
		fmt.Printf("❌ 查询目标ID 7失败: %v\n", err)
		return
	}

	fmt.Printf("ID: %d\n", id)
	fmt.Printf("名称: %s\n", name)
	fmt.Printf("URL: %s\n", url)
	fmt.Printf("分类: %s\n", category)
	fmt.Printf("描述: %s\n", description)
	fmt.Printf("活跃: %t\n", isActive)
}
