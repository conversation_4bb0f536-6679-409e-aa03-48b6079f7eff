package main

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接数据库
	db, err := sql.Open("mysql", "root:park%123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 调试FAQ系统搜索问题...")

	// 测试查询
	query := "热门话题"
	fmt.Printf("\n🔍 测试查询: '%s'\n", query)

	// 1. 检查数据库中的实际数据
	fmt.Println("\n📊 检查数据库中的实际数据:")
	checkActualData(db)

	// 2. 模拟FAQ系统的fallbackTextSearch逻辑
	fmt.Println("\n🔄 模拟FAQ系统的fallbackTextSearch逻辑:")
	simulateFAQSearch(db, query)

	// 3. 检查字符编码问题
	fmt.Println("\n🔤 检查字符编码问题:")
	checkEncodingIssues(db, query)

	// 4. 尝试不同的搜索方式
	fmt.Println("\n🔍 尝试不同的搜索方式:")
	tryDifferentSearches(db, query)
}

func checkActualData(db *sql.DB) {
	query := `
		SELECT id, question, answer, status, confidence
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY confidence DESC, created_at DESC
		LIMIT 15
	`

	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("📋 数据库中的学习知识:")
	count := 0
	for rows.Next() {
		var id int
		var question, answer, status string
		var confidence float32

		err := rows.Scan(&id, &question, &answer, &status, &confidence)
		if err != nil {
			continue
		}

		count++
		answerPreview := answer
		if len(answerPreview) > 50 {
			answerPreview = answerPreview[:50] + "..."
		}

		fmt.Printf("  %d. ID %d (状态: %s, 置信度: %.2f)\n", count, id, status, confidence)
		fmt.Printf("     问题: %s\n", question)
		fmt.Printf("     答案: %s\n", answerPreview)
		fmt.Println()
	}

	fmt.Printf("📊 总共找到 %d 条记录\n", count)
}

func simulateFAQSearch(db *sql.DB, searchQuery string) {
	// 完全模拟FAQ系统的fallbackTextSearch方法
	fmt.Printf("🔍 搜索查询: '%s'\n", searchQuery)

	// 第一步：获取所有数据
	sqlQuery := `
		SELECT id, question, answer, source, confidence, category,
		       keywords, context, learned_from, status, created_at
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY confidence DESC, created_at DESC
		LIMIT ?
	`

	limit := 3
	rows, err := db.Query(sqlQuery, limit*3) // 获取更多结果用于过滤
	if err != nil {
		fmt.Printf("❌ 数据库查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	// 收集所有结果
	var allResults []map[string]interface{}
	for rows.Next() {
		var id int
		var question, answer, source, category, keywords, context, learnedFrom, status, createdAt string
		var confidence float32

		err := rows.Scan(&id, &question, &answer, &source, &confidence, &category,
			&keywords, &context, &learnedFrom, &status, &createdAt)
		if err != nil {
			fmt.Printf("❌ 扫描行失败: %v\n", err)
			continue
		}

		result := map[string]interface{}{
			"id":           id,
			"question":     question,
			"answer":       answer,
			"source":       source,
			"confidence":   confidence,
			"category":     category,
			"keywords":     keywords,
			"context":      context,
			"learned_from": learnedFrom,
			"status":       status,
			"created_at":   createdAt,
		}
		allResults = append(allResults, result)
	}

	fmt.Printf("📊 从数据库获取到 %d 条记录\n", len(allResults))

	// 第二步：在应用层进行文本匹配过滤
	var matchedResults []map[string]interface{}
	queryLower := strings.ToLower(searchQuery)

	fmt.Printf("🔍 开始应用层过滤，查询词: '%s'\n", queryLower)

	for i, result := range allResults {
		question := result["question"].(string)
		answer := result["answer"].(string)

		questionLower := strings.ToLower(question)
		answerLower := strings.ToLower(answer)

		fmt.Printf("  检查记录 %d (ID %d):\n", i+1, result["id"].(int))
		fmt.Printf("    问题: '%s' -> '%s'\n", question, questionLower)
		fmt.Printf("    答案: '%s' -> '%s'\n", 
			answer[:min(50, len(answer))]+"...", 
			answerLower[:min(50, len(answerLower))]+"...")

		questionMatch := strings.Contains(questionLower, queryLower)
		answerMatch := strings.Contains(answerLower, queryLower)

		fmt.Printf("    问题匹配: %t, 答案匹配: %t\n", questionMatch, answerMatch)

		if questionMatch || answerMatch {
			matchedResults = append(matchedResults, result)
			fmt.Printf("    ✅ 匹配成功！\n")
			if len(matchedResults) >= limit {
				break
			}
		} else {
			fmt.Printf("    ❌ 不匹配\n")
		}
		fmt.Println()
	}

	fmt.Printf("🎯 最终匹配结果: %d 个\n", len(matchedResults))

	if len(matchedResults) > 0 {
		fmt.Println("✅ 匹配的记录:")
		for i, result := range matchedResults {
			fmt.Printf("  %d. ID %d: %s\n", i+1, result["id"].(int), result["question"].(string))
		}
	} else {
		fmt.Println("❌ 没有找到匹配的记录")
	}
}

func checkEncodingIssues(db *sql.DB, searchQuery string) {
	// 检查字符编码问题
	fmt.Printf("🔤 查询字符串: '%s'\n", searchQuery)
	fmt.Printf("🔤 查询字符串长度: %d\n", len(searchQuery))
	fmt.Printf("🔤 查询字符串字节: %v\n", []byte(searchQuery))

	// 检查数据库中的数据编码
	query := `
		SELECT question, answer
		FROM learned_knowledge
		WHERE question LIKE '%热门%' OR question LIKE '%话题%'
		LIMIT 3
	`

	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 编码检查查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("📋 数据库中包含相关字符的记录:")
	for rows.Next() {
		var question, answer string
		err := rows.Scan(&question, &answer)
		if err != nil {
			continue
		}

		fmt.Printf("  问题: '%s' (字节: %v)\n", question, []byte(question))
		fmt.Printf("  答案: '%s' (字节: %v)\n", answer[:min(30, len(answer))], []byte(answer[:min(30, len(answer))]))
		fmt.Println()
	}
}

func tryDifferentSearches(db *sql.DB, searchQuery string) {
	// 尝试不同的搜索方式
	searches := []string{
		"热门话题",
		"热门",
		"话题",
		"百度热搜",
		"热搜",
		"微信",
	}

	for _, search := range searches {
		fmt.Printf("\n🔍 尝试搜索: '%s'\n", search)

		query := `
			SELECT id, question, answer
			FROM learned_knowledge
			WHERE status IN ('approved', 'pending')
			AND (question LIKE CONCAT('%', ?, '%') OR answer LIKE CONCAT('%', ?, '%'))
			LIMIT 3
		`

		rows, err := db.Query(query, search, search)
		if err != nil {
			fmt.Printf("❌ 搜索失败: %v\n", err)
			continue
		}

		found := false
		for rows.Next() {
			var id int
			var question, answer string
			err := rows.Scan(&id, &question, &answer)
			if err != nil {
				continue
			}

			if !found {
				fmt.Println("  ✅ 找到匹配:")
				found = true
			}

			fmt.Printf("    ID %d: %s\n", id, question)
		}
		rows.Close()

		if !found {
			fmt.Println("  ❌ 未找到匹配")
		}
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
