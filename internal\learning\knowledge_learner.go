package learning

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"
	"unicode/utf8"

	"faq-system/internal/embedding"
	"faq-system/internal/logger"
	"faq-system/internal/vectorstore"
)

// KnowledgeLearner 知识学习引擎 - 从用户交互中学习新知识
type KnowledgeLearner struct {
	db          *sql.DB
	vectorStore *vectorstore.VectorStore
	embedClient *embedding.Client
}

// LearnedKnowledge 学习到的知识
type LearnedKnowledge struct {
	ID          int                    `json:"id"`
	Question    string                 `json:"question"`
	Answer      string                 `json:"answer"`
	Source      string                 `json:"source"`       // user_input, conversation, correction
	Confidence  float32                `json:"confidence"`   // 知识的可信度
	Category    string                 `json:"category"`     // 知识分类
	Keywords    []string               `json:"keywords"`     // 关键词
	Context     string                 `json:"context"`      // 学习上下文
	LearnedFrom string                 `json:"learned_from"` // 学习来源用户
	Status      string                 `json:"status"`       // pending, approved, rejected
	CreatedAt   time.Time              `json:"created_at"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ConversationPattern 对话模式
type ConversationPattern struct {
	UserInput    string  `json:"user_input"`
	SystemReply  string  `json:"system_reply"`
	UserResponse string  `json:"user_response"`
	IsCorrection bool    `json:"is_correction"`
	Confidence   float32 `json:"confidence"`
}

// NewKnowledgeLearner 创建知识学习引擎
func NewKnowledgeLearner(db *sql.DB, vectorStore *vectorstore.VectorStore, embedClient *embedding.Client) *KnowledgeLearner {
	return &KnowledgeLearner{
		db:          db,
		vectorStore: vectorStore,
		embedClient: embedClient,
	}
}

// LearnFromUserInput 从用户输入中学习知识
func (kl *KnowledgeLearner) LearnFromUserInput(userID, query, userResponse string, systemResponse string) error {
	logger.Infof("🧠 尝试从用户输入学习知识: %s", query)

	// 1. 检测是否是知识提供模式
	knowledge := kl.extractKnowledgeFromInput(query, userResponse)
	if knowledge != nil {
		knowledge.LearnedFrom = userID
		knowledge.Source = "user_input"
		knowledge.Status = "approved" // 直接设置为已批准状态

		// 保存学习到的知识
		if err := kl.saveLearnedKnowledge(knowledge); err != nil {
			return fmt.Errorf("保存学习知识失败: %v", err)
		}

		logger.Infof("✅ 学习到新知识: %s", knowledge.Question)
		return nil
	}

	// 2. 检测是否是纠错模式
	correction := kl.detectCorrection(query, systemResponse, userResponse)
	if correction != nil {
		correction.LearnedFrom = userID
		correction.Source = "correction"
		correction.Status = "pending"

		if err := kl.saveLearnedKnowledge(correction); err != nil {
			return fmt.Errorf("保存纠错知识失败: %v", err)
		}

		logger.Infof("✅ 学习到纠错知识: %s", correction.Question)
		return nil
	}

	// 3. 分析对话模式，寻找隐含知识
	implicitKnowledge := kl.extractImplicitKnowledge(query, userResponse)
	if implicitKnowledge != nil {
		implicitKnowledge.LearnedFrom = userID
		implicitKnowledge.Source = "conversation"
		implicitKnowledge.Status = "approved" // 直接设置为已批准状态
		implicitKnowledge.Confidence = 0.6    // 隐含知识置信度较低

		if err := kl.saveLearnedKnowledge(implicitKnowledge); err != nil {
			return fmt.Errorf("保存隐含知识失败: %v", err)
		}

		logger.Infof("✅ 学习到隐含知识: %s", implicitKnowledge.Question)
		return nil
	}

	return nil
}

// extractKnowledgeFromInput 从用户输入中提取知识
func (kl *KnowledgeLearner) extractKnowledgeFromInput(query, userResponse string) *LearnedKnowledge {
	// 模式1: "X是Y" 或 "X是一种Y"
	patterns := []string{
		`(.+?)是(.+)`,
		`(.+?)是一种(.+)`,
		`(.+?)是一个(.+)`,
		`(.+?)属于(.+)`,
		`(.+?)指的是(.+)`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(query)
		if len(matches) >= 3 {
			subject := strings.TrimSpace(matches[1])
			description := strings.TrimSpace(matches[2])

			// 过滤掉太短或无意义的内容
			if len(subject) < 2 || len(description) < 3 {
				continue
			}

			// 生成问题和答案
			generatedQuestion := fmt.Sprintf("什么是%s？", subject)
			generatedAnswer := fmt.Sprintf("%s是%s。", subject, description)

			// 安全检查：确保生成的问题和答案不相同
			if strings.TrimSpace(generatedQuestion) == strings.TrimSpace(generatedAnswer) {
				logger.Warnf("🚨 知识提取：生成的问题和答案相同，跳过: %s", generatedQuestion)
				continue
			}

			// 检查是否是循环定义（如"C#是C#"）
			if strings.Contains(strings.ToLower(description), strings.ToLower(subject)) && len(description) < len(subject)*2 {
				logger.Warnf("🚨 知识提取：检测到循环定义，跳过: %s是%s", subject, description)
				continue
			}

			return &LearnedKnowledge{
				Question:   generatedQuestion,
				Answer:     generatedAnswer,
				Category:   kl.categorizeKnowledge(subject, description),
				Keywords:   kl.extractKeywords(subject + " " + description),
				Context:    query,
				Confidence: 0.8,
				Status:     "approved", // 直接设置为已批准状态
				CreatedAt:  time.Now(),
				Metadata: map[string]interface{}{
					"extraction_pattern": pattern,
					"original_query":     query,
					"subject":            subject,
					"description":        description,
				},
			}
		}
	}

	// 模式2: 用户提供详细解释
	if len(userResponse) > 50 && strings.Contains(userResponse, query) {
		return &LearnedKnowledge{
			Question:   query,
			Answer:     userResponse,
			Category:   kl.categorizeKnowledge(query, userResponse),
			Keywords:   kl.extractKeywords(query + " " + userResponse),
			Context:    fmt.Sprintf("用户查询: %s, 用户回应: %s", query, userResponse),
			Confidence: 0.7,
			Status:     "approved", // 直接设置为已批准状态
			CreatedAt:  time.Now(),
			Metadata: map[string]interface{}{
				"extraction_type": "user_explanation",
				"original_query":  query,
				"user_response":   userResponse,
			},
		}
	}

	return nil
}

// detectCorrection 检测用户纠错
func (kl *KnowledgeLearner) detectCorrection(query, systemResponse, userResponse string) *LearnedKnowledge {
	// 检测纠错关键词
	correctionKeywords := []string{"不对", "错误", "不是", "应该是", "实际上", "其实", "正确的是"}

	userResponseLower := strings.ToLower(userResponse)
	for _, keyword := range correctionKeywords {
		if strings.Contains(userResponseLower, keyword) {
			// 提取正确答案
			correctAnswer := kl.extractCorrectAnswer(userResponse)
			if correctAnswer != "" {
				return &LearnedKnowledge{
					Question:   query,
					Answer:     correctAnswer,
					Category:   "correction",
					Keywords:   kl.extractKeywords(query + " " + correctAnswer),
					Context:    fmt.Sprintf("系统回答: %s, 用户纠错: %s", systemResponse, userResponse),
					Confidence: 0.9,        // 用户纠错的置信度很高
					Status:     "approved", // 直接设置为已批准状态
					CreatedAt:  time.Now(),
					Metadata: map[string]interface{}{
						"correction_type":   "user_correction",
						"original_answer":   systemResponse,
						"corrected_answer":  correctAnswer,
						"correction_signal": keyword,
					},
				}
			}
		}
	}

	return nil
}

// extractImplicitKnowledge 提取隐含知识
func (kl *KnowledgeLearner) extractImplicitKnowledge(query, userResponse string) *LearnedKnowledge {
	// 安全检查：确保问题和答案不相同
	if strings.TrimSpace(query) == strings.TrimSpace(userResponse) {
		logger.Warnf("🚨 隐含知识提取：问题和答案相同，跳过: %s", query)
		return nil
	}

	// 如果用户提供了详细信息，可能包含隐含知识
	if len(userResponse) > 30 {
		// 简单的隐含知识提取
		if strings.Contains(userResponse, "用于") || strings.Contains(userResponse, "可以") {
			// 再次检查答案是否有意义
			if len(strings.TrimSpace(userResponse)) < 10 {
				logger.Warnf("🚨 隐含知识提取：答案过短，跳过: %s", userResponse)
				return nil
			}

			return &LearnedKnowledge{
				Question:   query,
				Answer:     userResponse,
				Category:   "implicit",
				Keywords:   kl.extractKeywords(query + " " + userResponse),
				Context:    fmt.Sprintf("隐含知识提取: %s -> %s", query, userResponse),
				Confidence: 0.5,        // 隐含知识置信度较低
				Status:     "approved", // 直接设置为已批准状态
				CreatedAt:  time.Now(),
				Metadata: map[string]interface{}{
					"extraction_type": "implicit_knowledge",
					"source_query":    query,
					"source_response": userResponse,
				},
			}
		}
	}

	return nil
}

// categorizeKnowledge 知识分类
func (kl *KnowledgeLearner) categorizeKnowledge(subject, description string) string {
	techKeywords := []string{"编程", "语言", "框架", "数据库", "算法", "API", "技术", "开发", "软件"}
	businessKeywords := []string{"业务", "管理", "流程", "策略", "市场", "客户", "产品"}

	content := strings.ToLower(subject + " " + description)

	for _, keyword := range techKeywords {
		if strings.Contains(content, keyword) {
			return "technology"
		}
	}

	for _, keyword := range businessKeywords {
		if strings.Contains(content, keyword) {
			return "business"
		}
	}

	return "general"
}

// extractKeywords 提取关键词
func (kl *KnowledgeLearner) extractKeywords(text string) []string {
	// 简单的关键词提取
	words := strings.Fields(strings.ToLower(text))
	keywords := make(map[string]bool)

	// 过滤停用词
	stopWords := map[string]bool{
		"是": true, "的": true, "在": true, "有": true, "和": true, "与": true,
		"或": true, "但": true, "如果": true, "那么": true, "这个": true, "那个": true,
		"什么": true, "如何": true, "怎么": true, "为什么": true, "一种": true, "一个": true,
	}

	for _, word := range words {
		if len(word) > 1 && !stopWords[word] {
			keywords[word] = true
		}
	}

	result := make([]string, 0, len(keywords))
	for keyword := range keywords {
		result = append(result, keyword)
	}

	return result
}

// extractCorrectAnswer 提取正确答案
func (kl *KnowledgeLearner) extractCorrectAnswer(userResponse string) string {
	// 寻找"应该是"、"正确的是"等后面的内容
	patterns := []string{
		`应该是(.+)`,
		`正确的是(.+)`,
		`实际上(.+)`,
		`其实(.+)`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(userResponse)
		if len(matches) >= 2 {
			return strings.TrimSpace(matches[1])
		}
	}

	return ""
}

// SaveKnowledge 公共方法：保存学习到的知识
func (kl *KnowledgeLearner) SaveKnowledge(knowledge *LearnedKnowledge) error {
	return kl.saveLearnedKnowledge(knowledge)
}

// saveLearnedKnowledge 保存学习到的知识
func (kl *KnowledgeLearner) saveLearnedKnowledge(knowledge *LearnedKnowledge) error {
	// 字符编码清理：确保数据库兼容性
	knowledge.Question = kl.sanitizeForDatabase(knowledge.Question)
	knowledge.Answer = kl.sanitizeForDatabase(knowledge.Answer)
	knowledge.Context = kl.sanitizeForDatabase(knowledge.Context)
	knowledge.Category = kl.sanitizeForDatabase(knowledge.Category)
	knowledge.LearnedFrom = kl.sanitizeForDatabase(knowledge.LearnedFrom)

	// 最终安全检查：确保问题和答案不相同
	if strings.TrimSpace(knowledge.Question) == strings.TrimSpace(knowledge.Answer) {
		logger.Warnf("🚨 保存知识：问题和答案相同，拒绝保存: %s", knowledge.Question)
		return fmt.Errorf("问题和答案不能相同")
	}

	// 检查问题和答案是否都有意义
	if len(strings.TrimSpace(knowledge.Question)) < 3 || len(strings.TrimSpace(knowledge.Answer)) < 3 {
		logger.Warnf("🚨 保存知识：问题或答案过短，拒绝保存: Q=%s, A=%s", knowledge.Question, knowledge.Answer)
		return fmt.Errorf("问题或答案过短")
	}

	keywordsJSON, _ := json.Marshal(knowledge.Keywords)
	metadataJSON, _ := json.Marshal(knowledge.Metadata)

	query := `
		INSERT INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status, metadata)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := kl.db.Exec(query,
		knowledge.Question, knowledge.Answer, knowledge.Source, knowledge.Confidence,
		knowledge.Category, keywordsJSON, knowledge.Context, knowledge.LearnedFrom,
		knowledge.Status, metadataJSON)

	if err != nil {
		return err
	}

	// 获取插入的ID
	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	knowledge.ID = int(id)

	// 生成并存储向量
	if err := kl.generateAndStoreVector(knowledge); err != nil {
		logger.Warnf("生成学习知识向量失败: %v", err)
	}

	return nil
}

// generateAndStoreVector 生成并存储知识向量
func (kl *KnowledgeLearner) generateAndStoreVector(knowledge *LearnedKnowledge) error {
	if kl.embedClient == nil {
		return fmt.Errorf("embedding client not available")
	}

	// 生成问题+答案的组合向量
	combinedText := knowledge.Question + " " + knowledge.Answer
	vector, err := kl.embedClient.EmbedText(combinedText)
	if err != nil {
		return err
	}

	// 存储向量
	vectorJSON, _ := json.Marshal(vector)
	query := `
		INSERT INTO knowledge_vectors (knowledge_id, vector_data, vector_type)
		VALUES (?, ?, 'combined')
	`

	_, err = kl.db.Exec(query, knowledge.ID, vectorJSON)
	return err
}

// sanitizeForDatabase 清理字符串以确保数据库兼容性
func (kl *KnowledgeLearner) sanitizeForDatabase(s string) string {
	if s == "" {
		return s
	}

	// 1. 移除NULL字符和其他控制字符
	s = strings.ReplaceAll(s, "\x00", "")
	s = strings.ReplaceAll(s, "\x01", "")
	s = strings.ReplaceAll(s, "\x02", "")
	s = strings.ReplaceAll(s, "\x03", "")
	s = strings.ReplaceAll(s, "\x04", "")
	s = strings.ReplaceAll(s, "\x05", "")
	s = strings.ReplaceAll(s, "\x06", "")
	s = strings.ReplaceAll(s, "\x07", "")
	s = strings.ReplaceAll(s, "\x08", "")
	// 保留\x09 (tab), \x0A (LF), \x0D (CR)
	s = strings.ReplaceAll(s, "\x0B", "")
	s = strings.ReplaceAll(s, "\x0C", "")
	s = strings.ReplaceAll(s, "\x0E", "")
	s = strings.ReplaceAll(s, "\x0F", "")

	// 2. 确保UTF-8编码有效性
	if !utf8.ValidString(s) {
		// 将无效的UTF-8字符替换为问号
		s = strings.ToValidUTF8(s, "?")
	}

	// 3. 移除MySQL不支持的4字节UTF-8字符（emoji等）
	// 如果数据库使用utf8而不是utf8mb4
	s = regexp.MustCompile(`[\x{10000}-\x{10FFFF}]`).ReplaceAllString(s, "")

	// 4. 限制长度避免数据库字段溢出
	maxLength := 65535 // TEXT字段的最大长度
	if len(s) > maxLength {
		s = s[:maxLength]
	}

	// 5. 清理多余的空白字符
	s = strings.TrimSpace(s)

	return s
}
