package server

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"faq-system/internal/config"
	"faq-system/internal/crawler"
	"faq-system/internal/health"
	"faq-system/internal/learning"
	"faq-system/internal/logger"
	"faq-system/internal/rag"
	"faq-system/internal/server/handlers"
	"faq-system/internal/server/templates"

	"github.com/gin-gonic/gin"
)

// Server Web服务器
type Server struct {
	config          *config.Config
	engine          *gin.Engine
	ragSystem       *rag.ChatSystem
	healthChecker   *health.Checker
	httpServer      *http.Server
	learningManager *learning.Manager
	crawler         *crawler.KnowledgeCrawler
}

// New 创建新的Web服务器
func New(cfg *config.Config, ragSystem *rag.ChatSystem, healthChecker *health.Checker, learningManager *learning.Manager, knowledgeCrawler *crawler.KnowledgeCrawler) (*Server, error) {
	// 设置Gin模式
	if cfg.Server.Host != "0.0.0.0" {
		gin.SetMode(gin.ReleaseMode)
	}

	server := &Server{
		config:          cfg,
		engine:          gin.Default(),
		ragSystem:       ragSystem,
		healthChecker:   healthChecker,
		learningManager: learningManager,
		crawler:         knowledgeCrawler,
	}

	// 设置路由
	server.setupRoutes()

	return server, nil
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 创建处理器
	h := handlers.New(s.ragSystem, s.healthChecker)
	kh := NewKnowledgeHandler(s.ragSystem)

	// 主页 - 智能问答界面
	s.engine.GET("/", func(c *gin.Context) {
		c.Header("Content-Type", "text/html; charset=utf-8")
		c.String(http.StatusOK, templates.GetIndexHTML())
	})

	// 健康检查
	s.engine.GET("/health", h.HealthCheck)

	// 问答接口
	s.engine.POST("/ask", h.AskQuestion)

	// 测试页面
	s.engine.GET("/test", func(c *gin.Context) {
		c.Header("Content-Type", "text/html; charset=utf-8")
		c.String(http.StatusOK, templates.GetTestHTML())
	})

	// API路由组
	api := s.engine.Group("/api/v1")
	{
		api.GET("/health", h.HealthCheck)
		api.POST("/ask", h.AskQuestion)
		api.GET("/stats", h.GetStats)
	}

	// 学习系统API路由
	if s.learningManager != nil {
		learningAPI := s.learningManager.GetAPI()
		learning := s.engine.Group("/api/learning")
		{
			learning.POST("/feedback", gin.WrapF(learningAPI.HandleFeedback))
			learning.POST("/behavior", gin.WrapF(learningAPI.HandleBehavior))
			learning.GET("/metrics", gin.WrapF(learningAPI.HandleMetrics))
			learning.GET("/performance", gin.WrapF(learningAPI.HandlePerformanceStats))
			learning.GET("/activity", gin.WrapF(learningAPI.HandleRecentActivity))
			learning.GET("/recommendations", gin.WrapF(learningAPI.HandleOptimizationRecommendations))
			learning.POST("/analyze", gin.WrapF(learningAPI.HandleLearningAnalysis))
			learning.GET("/config", gin.WrapF(learningAPI.HandleLearningConfig))
			learning.PUT("/config", gin.WrapF(learningAPI.HandleLearningConfig))
		}
	}

	// 知识学习API路由
	knowledge := s.engine.Group("/api/knowledge")
	{
		knowledge.POST("/teach", kh.TeachKnowledge)
		knowledge.GET("/search", kh.SearchKnowledge)
		knowledge.GET("/list", kh.GetLearnedKnowledge)
		knowledge.GET("/pending", kh.GetPendingKnowledge)
		knowledge.POST("/:id/approve", kh.ApproveKnowledge)
		knowledge.POST("/:id/feedback", kh.FeedbackKnowledge)
		knowledge.GET("/stats", kh.GetKnowledgeStats)
	}

	// 爬虫系统API路由
	if s.crawler != nil {
		// 手动注册爬虫路由
		crawlerGroup := s.engine.Group("/api/crawler")
		{
			// 爬虫管理
			crawlerGroup.POST("/start", s.startCrawler)
			crawlerGroup.POST("/stop", s.stopCrawler)
			crawlerGroup.GET("/status", s.getCrawlerStatus)

			// 目标管理
			crawlerGroup.GET("/targets", s.getTargets)
			crawlerGroup.POST("/targets", s.addTarget)
			crawlerGroup.GET("/targets/:id", s.getTarget)
			crawlerGroup.PUT("/targets/:id", s.updateTarget)
			crawlerGroup.DELETE("/targets/:id", s.deleteTarget)
			crawlerGroup.POST("/targets/:id/crawl", s.manualCrawl)
			crawlerGroup.POST("/targets/:id/enable", s.enableTarget)
			crawlerGroup.POST("/targets/:id/disable", s.disableTarget)

			// 结果查看
			crawlerGroup.GET("/results", s.getResults)
			crawlerGroup.GET("/statistics", s.getStatistics)
			crawlerGroup.GET("/logs", s.getLogs)
		}
		logger.Info("✅ 爬虫API路由已启用")
	}

	// 静态文件服务 - 支持学习仪表板和爬虫仪表板
	s.engine.Static("/static", "../web")
	s.engine.StaticFile("/learning_dashboard.html", "../web/learning_dashboard.html")
	s.engine.StaticFile("/crawler_dashboard.html", "../web/crawler_dashboard.html")
}

// Start 启动服务器
func (s *Server) Start() error {
	address := s.config.GetServerAddress()

	s.httpServer = &http.Server{
		Addr:         address,
		Handler:      s.engine,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	logger.Infof("Server starting on %s", address)
	return s.httpServer.ListenAndServe()
}

// Shutdown 关闭服务器
func (s *Server) Shutdown() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	logger.Info("Shutting down server...")
	return s.httpServer.Shutdown(ctx)
}

// 爬虫API处理器方法

// startCrawler 启动爬虫
func (s *Server) startCrawler(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	if err := s.crawler.Start(); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "爬虫启动成功"})
}

// stopCrawler 停止爬虫
func (s *Server) stopCrawler(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	s.crawler.Stop()
	c.JSON(200, gin.H{"success": true, "message": "爬虫停止成功"})
}

// getCrawlerStatus 获取爬虫状态
func (s *Server) getCrawlerStatus(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	targets := s.crawler.GetTargets()
	activeCrawls := s.crawler.GetActiveCrawls()

	activeTargets := 0
	for _, target := range targets {
		if target.Enabled {
			activeTargets++
		}
	}

	c.JSON(200, gin.H{
		"success": true,
		"data": gin.H{
			"running":        s.crawler.IsRunning(),
			"total_targets":  len(targets),
			"active_targets": activeTargets,
			"crawling_now":   len(activeCrawls),
			"active_crawls":  activeCrawls,
		},
	})
}

// getTargets 获取所有爬取目标
func (s *Server) getTargets(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	targets := s.crawler.GetTargets()
	c.JSON(200, gin.H{"success": true, "data": targets, "total": len(targets)})
}

// addTarget 添加爬取目标
func (s *Server) addTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	var target crawler.CrawlTarget
	if err := c.ShouldBindJSON(&target); err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的JSON数据"})
		return
	}

	if err := s.crawler.AddTarget(&target); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "爬取目标添加成功", "data": target})
}

// getTarget 获取单个爬取目标
func (s *Server) getTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	targets := s.crawler.GetTargets()

	for _, target := range targets {
		if fmt.Sprintf("%d", target.ID) == id {
			c.JSON(200, gin.H{"success": true, "data": target})
			return
		}
	}

	c.JSON(404, gin.H{"success": false, "message": "目标不存在"})
}

// updateTarget 更新爬取目标
func (s *Server) updateTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	var target crawler.CrawlTarget
	if err := c.ShouldBindJSON(&target); err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的JSON数据"})
		return
	}

	// 设置ID
	if targetID, err := strconv.Atoi(id); err == nil {
		target.ID = targetID
	} else {
		c.JSON(400, gin.H{"success": false, "message": "无效的ID"})
		return
	}

	if err := s.crawler.UpdateTarget(&target); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "爬取目标更新成功", "data": target})
}

// deleteTarget 删除爬取目标
func (s *Server) deleteTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	targetID, err := strconv.Atoi(id)
	if err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的ID"})
		return
	}

	if err := s.crawler.RemoveTarget(targetID); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "爬取目标删除成功"})
}

// manualCrawl 手动触发爬取
func (s *Server) manualCrawl(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	targetID, err := strconv.Atoi(id)
	if err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的ID"})
		return
	}

	if err := s.crawler.ManualCrawl(targetID); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "手动爬取任务已启动"})
}

// enableTarget 启用目标
func (s *Server) enableTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	targetID, err := strconv.Atoi(id)
	if err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的目标ID"})
		return
	}

	if err := s.crawler.EnableTarget(targetID); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "目标启用成功"})
}

// disableTarget 禁用目标
func (s *Server) disableTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	targetID, err := strconv.Atoi(id)
	if err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的目标ID"})
		return
	}

	if err := s.crawler.DisableTarget(targetID); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "目标禁用成功"})
}

// getResults 获取爬取结果
func (s *Server) getResults(c *gin.Context) {
	c.JSON(200, gin.H{"success": true, "data": []interface{}{}, "total": 0})
}

// getStatistics 获取爬取统计
func (s *Server) getStatistics(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	targets := s.crawler.GetTargets()
	activeTargets := 0
	for _, target := range targets {
		if target.Enabled {
			activeTargets++
		}
	}

	c.JSON(200, gin.H{
		"success": true,
		"data": gin.H{
			"total_targets":     len(targets),
			"active_targets":    activeTargets,
			"total_crawls":      0,
			"successful_crawls": 0,
			"failed_crawls":     0,
		},
	})
}

// getLogs 获取爬取日志
func (s *Server) getLogs(c *gin.Context) {
	c.JSON(200, gin.H{"success": true, "data": []interface{}{}, "total": 0})
}
