package database

import (
	"database/sql"
	"faq-system/internal/config"
	"faq-system/internal/logger"
	"fmt"

	_ "github.com/go-sql-driver/mysql"
)

// Initialize 初始化数据库
func Initialize(cfg *config.Config) error {
	logger.Info("开始初始化数据库...")

	// 1. 连接MySQL服务器（不指定数据库）- 使用原来的方法
	serverDSN := fmt.Sprintf("%s:%s@tcp(%s:%s)/?charset=utf8mb4&parseTime=True&loc=Local&collation=utf8mb4_general_ci",
		cfg.MySQL.Username,
		cfg.MySQL.Password,
		cfg.MySQL.Host,
		cfg.MySQL.Port,
	)

	logger.Infof("尝试连接MySQL服务器: %s:%s", cfg.MySQL.Host, cfg.MySQL.Port)
	db, err := sql.Open("mysql", serverDSN)
	if err != nil {
		return err
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		return err
	}
	logger.Info("成功连接到MySQL服务器")

	// 打印MySQL字符集配置信息
	printMySQLCharsetInfo(db)

	// 2. 创建数据库，使用utf8mb4_general_ci
	_, err = db.Exec("CREATE DATABASE IF NOT EXISTS " + cfg.MySQL.Database + " CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci")
	if err != nil {
		return err
	}
	logger.Infof("数据库 '%s' 创建成功或已存在", cfg.MySQL.Database)

	// 设置会话字符集，确保一致性
	_, err = db.Exec("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci")
	if err != nil {
		logger.Warnf("设置会话字符集失败: %v", err)
	}

	// 3. 连接到指定数据库 - 使用原来的方法
	dbDSN := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local&collation=utf8mb4_general_ci",
		cfg.MySQL.Username,
		cfg.MySQL.Password,
		cfg.MySQL.Host,
		cfg.MySQL.Port,
		cfg.MySQL.Database,
	)

	faqDB, err := sql.Open("mysql", dbDSN)
	if err != nil {
		return err
	}
	defer faqDB.Close()

	// 确保连接使用正确的字符集
	_, err = faqDB.Exec("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci")
	if err != nil {
		logger.Warnf("设置数据库连接字符集失败: %v", err)
	}

	// 4. 创建FAQ表 - 使用与原系统一致的表名 'faq'
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS faq (
		id INT AUTO_INCREMENT PRIMARY KEY,
		question TEXT NOT NULL,
		answer TEXT NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;`

	_, err = faqDB.Exec(createTableSQL)
	if err != nil {
		return err
	}
	logger.Info("FAQ表创建成功或已存在")

	// 5. 检查是否需要插入示例数据
	var count int
	err = faqDB.QueryRow("SELECT COUNT(*) FROM faq").Scan(&count)
	if err != nil {
		return err
	}

	if count == 0 {
		logger.Info("插入示例FAQ数据...")
		if err := insertSampleData(faqDB); err != nil {
			logger.Warnf("插入示例数据失败: %v", err)
		} else {
			logger.Info("示例数据插入成功")
		}
	} else {
		logger.Infof("FAQ表中已有 %d 条数据，跳过示例数据插入", count)
	}

	// 5. 初始化学习相关表
	logger.Info("初始化学习相关表...")
	if err := initializeLearningTables(faqDB); err != nil {
		logger.Errorf("初始化学习表失败: %v", err)
		return fmt.Errorf("初始化学习表失败: %v", err)
	}
	logger.Info("学习相关表初始化完成")

	// 6. 初始化爬虫相关表
	logger.Info("初始化爬虫相关表...")
	if err := initializeCrawlerTables(faqDB); err != nil {
		logger.Errorf("初始化爬虫表失败: %v", err)
		return fmt.Errorf("初始化爬虫表失败: %v", err)
	}
	logger.Info("爬虫相关表初始化完成")

	logger.Info("数据库初始化完成")
	return nil
}

// insertSampleData 插入示例数据
func insertSampleData(db *sql.DB) error {
	sampleFAQs := []struct {
		question string
		answer   string
	}{
		{
			"什么是LocalAI？",
			"LocalAI是一个开源的本地AI推理引擎，让您可以在自己的设备上运行大语言模型。它支持多种AI模型格式，兼容OpenAI API，完全本地化部署，保护数据隐私。",
		},
		{
			"如何使用MySQL存储FAQ？",
			"MySQL是世界上最流行的开源关系型数据库管理系统。在这个FAQ系统中，MySQL负责存储所有的FAQ问题和答案，提供可靠的数据持久化和复杂的数据查询操作。",
		},
		{
			"向量搜索的原理是什么？",
			"向量搜索是现代AI系统的核心技术，通过将文本转换为高维数字向量（embedding），然后计算向量间的相似度来找到相关内容。即使用词不同，语义相似的内容也能被准确找到。",
		},
		{
			"Go语言有什么优势？",
			"Go语言是Google开发的现代编程语言，这个FAQ系统就是用Go构建的！ 🚀 Go语言的优势： • 语法简洁清晰，学习曲线平缓 • 内置强大的并发支持（goroutines） • 编译速度极快，运行效率高 • 丰富的标准库，开发效率高 • 跨平台支持，部署简单",
		},
		{
			"Go语言开发环境配置",
			"Go语言开发环境配置详细步骤：\n\n🔧 **环境安装**\n1. 从官网 https://golang.org 下载Go安装包\n2. 安装到系统目录（如 /usr/local/go 或 C:\\Go）\n\n⚙️ **环境变量配置**\n1. 设置 GOROOT：Go安装路径\n2. 设置 GOPATH：工作目录路径\n3. 添加 $GOROOT/bin 到 PATH\n\n📁 **工作目录结构**\n```\nGOPATH/\n├── src/     # 源代码\n├── pkg/     # 编译包\n└── bin/     # 可执行文件\n```\n\n🛠️ **开发工具配置**\n• VS Code + Go扩展\n• GoLand IDE\n• Vim + vim-go\n\n✅ **验证安装**\n```bash\ngo version\ngo env\n```",
		},
		{
			"如何安装Go语言？",
			"Go语言安装指南：\n\n📥 **下载安装包**\n• 访问 https://golang.org/dl/\n• 选择适合您操作系统的版本\n\n🖥️ **Windows安装**\n1. 下载 .msi 安装包\n2. 双击运行，按提示安装\n3. 默认安装到 C:\\Go\n\n🐧 **Linux安装**\n```bash\nwget https://golang.org/dl/go1.21.0.linux-amd64.tar.gz\nsudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz\nexport PATH=$PATH:/usr/local/go/bin\n```\n\n🍎 **macOS安装**\n```bash\n# 使用Homebrew\nbrew install go\n\n# 或下载pkg安装包\n```\n\n✅ **验证安装**\n```bash\ngo version\n```",
		},
		{
			"什么是向量数据库？",
			"向量数据库是专门用于存储和检索高维向量数据的数据库系统。它能够高效地进行向量相似度搜索，是现代AI应用中实现语义搜索和推荐系统的重要基础设施。",
		},
		{
			"如何部署FAQ系统？",
			"FAQ系统的部署包括：1) 安装MySQL数据库；2) 配置Go运行环境；3) 设置LocalAI服务（可选）；4) 配置系统参数；5) 启动FAQ服务。系统支持Docker部署和传统部署两种方式。",
		},
	}

	for _, faq := range sampleFAQs {
		_, err := db.Exec("INSERT INTO faq (question, answer) VALUES (?, ?)", faq.question, faq.answer)
		if err != nil {
			return err
		}
	}

	return nil
}

// initializeLearningTables 初始化学习相关表
func initializeLearningTables(db *sql.DB) error {
	// 学习表的SQL定义
	learningTables := []string{
		// 1. 用户查询记录表
		`CREATE TABLE IF NOT EXISTS user_queries (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
			user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
			query_text TEXT NOT NULL COMMENT '用户查询内容',
			query_intent VARCHAR(50) COMMENT '识别的意图',
			query_type VARCHAR(50) COMMENT '问题类型',
			query_embedding JSON COMMENT '查询向量（JSON格式）',
			context_data JSON COMMENT '上下文信息',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_user_id (user_id),
			INDEX idx_session_id (session_id),
			INDEX idx_created_at (created_at),
			INDEX idx_intent (query_intent)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户查询记录表'`,

		// 2. 系统响应记录表
		`CREATE TABLE IF NOT EXISTS system_responses (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			query_id BIGINT NOT NULL COMMENT '关联的查询ID',
			matched_faq_id INT COMMENT '匹配的FAQ ID',
			response_text TEXT NOT NULL COMMENT '系统回答内容',
			response_source VARCHAR(100) COMMENT '回答来源',
			confidence_score FLOAT COMMENT '置信度得分',
			match_type VARCHAR(50) COMMENT '匹配类型',
			processing_time_ms INT COMMENT '处理时间（毫秒）',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
			FOREIGN KEY (matched_faq_id) REFERENCES faq(id) ON DELETE SET NULL,
			INDEX idx_query_id (query_id),
			INDEX idx_faq_id (matched_faq_id),
			INDEX idx_confidence (confidence_score)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统响应记录表'`,

		// 3. 用户反馈表
		`CREATE TABLE IF NOT EXISTS user_feedback (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			query_id BIGINT NOT NULL COMMENT '关联的查询ID',
			response_id BIGINT NOT NULL COMMENT '关联的响应ID',
			feedback_type ENUM('helpful', 'not_helpful', 'partially_helpful') NOT NULL COMMENT '反馈类型',
			rating INT CHECK (rating >= 1 AND rating <= 5) COMMENT '评分(1-5)',
			feedback_text TEXT COMMENT '文字反馈',
			improvement_suggestion TEXT COMMENT '改进建议',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
			FOREIGN KEY (response_id) REFERENCES system_responses(id) ON DELETE CASCADE,
			INDEX idx_query_id (query_id),
			INDEX idx_response_id (response_id),
			INDEX idx_feedback_type (feedback_type),
			INDEX idx_rating (rating)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户反馈表'`,

		// 4. 用户行为追踪表
		`CREATE TABLE IF NOT EXISTS user_behaviors (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			query_id BIGINT NOT NULL COMMENT '关联的查询ID',
			behavior_type ENUM('click', 'scroll', 'copy', 'follow_up', 'exit') NOT NULL COMMENT '行为类型',
			behavior_data JSON COMMENT '行为详细数据',
			timestamp_ms BIGINT COMMENT '行为时间戳（毫秒）',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
			INDEX idx_query_id (query_id),
			INDEX idx_behavior_type (behavior_type),
			INDEX idx_timestamp (timestamp_ms)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户行为追踪表'`,

		// 5. 学习模式表
		`CREATE TABLE IF NOT EXISTS learning_patterns (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			pattern_type ENUM('query_similarity', 'intent_mapping', 'response_optimization', 'user_preference') NOT NULL COMMENT '模式类型',
			pattern_name VARCHAR(200) NOT NULL COMMENT '模式名称',
			pattern_data JSON NOT NULL COMMENT '模式数据',
			confidence FLOAT DEFAULT 0.0 COMMENT '模式置信度',
			usage_count INT DEFAULT 0 COMMENT '使用次数',
			success_rate FLOAT DEFAULT 0.0 COMMENT '成功率',
			last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_pattern_type (pattern_type),
			INDEX idx_confidence (confidence),
			INDEX idx_success_rate (success_rate),
			UNIQUE KEY uk_pattern_name (pattern_name)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='学习模式表'`,

		// 6. FAQ性能统计表
		`CREATE TABLE IF NOT EXISTS faq_performance (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			faq_id INT NOT NULL COMMENT 'FAQ ID',
			query_count INT DEFAULT 0 COMMENT '被查询次数',
			match_count INT DEFAULT 0 COMMENT '被匹配次数',
			positive_feedback INT DEFAULT 0 COMMENT '正面反馈数',
			negative_feedback INT DEFAULT 0 COMMENT '负面反馈数',
			avg_confidence FLOAT DEFAULT 0.0 COMMENT '平均置信度',
			avg_rating FLOAT DEFAULT 0.0 COMMENT '平均评分',
			last_matched TIMESTAMP COMMENT '最后匹配时间',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			FOREIGN KEY (faq_id) REFERENCES faq(id) ON DELETE CASCADE,
			UNIQUE KEY uk_faq_id (faq_id),
			INDEX idx_match_count (match_count),
			INDEX idx_avg_rating (avg_rating)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='FAQ性能统计表'`,

		// 7. 系统学习配置表
		`CREATE TABLE IF NOT EXISTS learning_config (
			id INT AUTO_INCREMENT PRIMARY KEY,
			config_key VARCHAR(100) NOT NULL COMMENT '配置键',
			config_value TEXT NOT NULL COMMENT '配置值',
			config_type ENUM('string', 'int', 'float', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
			description TEXT COMMENT '配置描述',
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			UNIQUE KEY uk_config_key (config_key)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统学习配置表'`,

		// 8. 学习知识表 - 从create_knowledge_learning_tables.go集成
		`CREATE TABLE IF NOT EXISTS learned_knowledge (
			id INT AUTO_INCREMENT PRIMARY KEY,
			question TEXT NOT NULL COMMENT '学习到的问题',
			answer TEXT NOT NULL COMMENT '学习到的答案',
			source ENUM('user_input', 'conversation', 'correction', 'implicit') NOT NULL COMMENT '知识来源',
			confidence FLOAT DEFAULT 0.0 COMMENT '知识置信度',
			category VARCHAR(100) DEFAULT 'general' COMMENT '知识分类',
			keywords JSON COMMENT '关键词',
			context TEXT COMMENT '学习上下文',
			learned_from VARCHAR(100) COMMENT '学习来源用户',
			status ENUM('pending', 'approved', 'rejected', 'integrated') DEFAULT 'pending' COMMENT '状态',
			metadata JSON COMMENT '元数据',
			usage_count INT DEFAULT 0 COMMENT '使用次数',
			success_rate FLOAT DEFAULT 0.0 COMMENT '成功率',
			last_used TIMESTAMP NULL COMMENT '最后使用时间',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_source (source),
			INDEX idx_category (category),
			INDEX idx_status (status),
			INDEX idx_confidence (confidence),
			INDEX idx_learned_from (learned_from),
			INDEX idx_created_at (created_at)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='学习知识表'`,

		// 9. 知识向量表
		`CREATE TABLE IF NOT EXISTS knowledge_vectors (
			id INT AUTO_INCREMENT PRIMARY KEY,
			knowledge_id INT NOT NULL COMMENT '知识ID',
			vector_data JSON NOT NULL COMMENT '向量数据',
			vector_type ENUM('question', 'answer', 'combined') DEFAULT 'combined' COMMENT '向量类型',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_knowledge_id (knowledge_id),
			INDEX idx_vector_type (vector_type),
			FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识向量表'`,

		// 10. 知识反馈表
		`CREATE TABLE IF NOT EXISTS knowledge_feedback (
			id INT AUTO_INCREMENT PRIMARY KEY,
			knowledge_id INT NOT NULL COMMENT '知识ID',
			user_id VARCHAR(100) NOT NULL COMMENT '用户ID',
			feedback_type ENUM('helpful', 'not_helpful', 'incorrect', 'incomplete') NOT NULL COMMENT '反馈类型',
			rating INT DEFAULT 0 COMMENT '评分 1-5',
			comment TEXT COMMENT '反馈评论',
			context TEXT COMMENT '反馈上下文',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_knowledge_id (knowledge_id),
			INDEX idx_user_id (user_id),
			INDEX idx_feedback_type (feedback_type),
			INDEX idx_rating (rating),
			FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识反馈表'`,

		// 11. 知识使用记录表
		`CREATE TABLE IF NOT EXISTS knowledge_usage (
			id INT AUTO_INCREMENT PRIMARY KEY,
			knowledge_id INT NOT NULL COMMENT '知识ID',
			query_id BIGINT COMMENT '查询ID',
			user_id VARCHAR(100) NOT NULL COMMENT '用户ID',
			match_score FLOAT DEFAULT 0.0 COMMENT '匹配分数',
			was_helpful BOOLEAN DEFAULT NULL COMMENT '是否有帮助',
			response_time INT DEFAULT 0 COMMENT '响应时间(ms)',
			context TEXT COMMENT '使用上下文',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_knowledge_id (knowledge_id),
			INDEX idx_query_id (query_id),
			INDEX idx_user_id (user_id),
			INDEX idx_match_score (match_score),
			INDEX idx_was_helpful (was_helpful),
			INDEX idx_created_at (created_at),
			FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识使用记录表'`,
	}

	// 创建表
	for _, tableSQL := range learningTables {
		if _, err := db.Exec(tableSQL); err != nil {
			return fmt.Errorf("创建学习表失败: %v", err)
		}
	}

	// 插入默认学习配置
	defaultConfigs := []struct {
		key         string
		value       string
		configType  string
		description string
	}{
		{"learning_enabled", "true", "boolean", "是否启用学习功能"},
		{"min_feedback_threshold", "5", "int", "最小反馈数量阈值"},
		{"confidence_learning_rate", "0.1", "float", "置信度学习率"},
		{"pattern_update_interval", "3600", "int", "模式更新间隔（秒）"},
		{"max_learning_patterns", "1000", "int", "最大学习模式数量"},
		{"feedback_weight_positive", "1.0", "float", "正面反馈权重"},
		{"feedback_weight_negative", "2.0", "float", "负面反馈权重"},
		{"similarity_threshold", "0.8", "float", "相似度阈值"},
		{"auto_optimize_enabled", "true", "boolean", "是否启用自动优化"},
		{"learning_batch_size", "100", "int", "学习批次大小"},
	}

	for _, config := range defaultConfigs {
		_, err := db.Exec(`
			INSERT INTO learning_config (config_key, config_value, config_type, description)
			VALUES (?, ?, ?, ?)
			ON DUPLICATE KEY UPDATE
			config_value = VALUES(config_value),
			updated_at = CURRENT_TIMESTAMP
		`, config.key, config.value, config.configType, config.description)

		if err != nil {
			return fmt.Errorf("插入默认学习配置失败: %v", err)
		}
	}

	// 插入示例学习知识
	logger.Info("插入示例学习知识...")
	if err := insertSampleLearningKnowledge(db); err != nil {
		logger.Warnf("插入示例学习知识失败: %v", err)
	} else {
		logger.Info("示例学习知识插入成功")
	}

	logger.Info("学习相关表初始化完成")
	return nil
}

// insertSampleLearningKnowledge 插入示例学习知识
func insertSampleLearningKnowledge(db *sql.DB) error {
	// 检查是否已有学习知识
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		logger.Infof("学习知识表中已有 %d 条数据，跳过示例数据插入", count)
		return nil
	}

	sampleKnowledge := []string{
		`INSERT IGNORE INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES
		('什么是C#？', 'C#是微软开发的一种面向对象的编程语言，运行在.NET框架上。', 'user_input', 0.9, 'technology', '["c#", "编程语言", "微软", ".net"]', '用户教学输入', 'system', 'approved')`,

		`INSERT IGNORE INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES
		('什么是Python？', 'Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。', 'user_input', 0.9, 'technology', '["python", "编程语言", "高级语言"]', '用户教学输入', 'system', 'approved')`,

		`INSERT IGNORE INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES
		('什么是JavaScript？', 'JavaScript是一种主要用于网页开发的脚本语言，可以实现动态交互效果。', 'user_input', 0.9, 'technology', '["javascript", "脚本语言", "网页开发"]', '用户教学输入', 'system', 'approved')`,

		`INSERT IGNORE INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES
		('什么是Java？', 'Java是一种跨平台的面向对象编程语言，广泛用于企业级应用开发。', 'user_input', 0.9, 'technology', '["java", "编程语言", "跨平台", "企业级"]', '用户教学输入', 'system', 'approved')`,
	}

	for i, knowledgeSQL := range sampleKnowledge {
		if _, err := db.Exec(knowledgeSQL); err != nil {
			logger.Warnf("插入示例知识 %d 失败: %v", i+1, err)
		} else {
			logger.Infof("✅ 插入示例知识 %d/4", i+1)
		}
	}

	return nil
}

// initializeCrawlerTables 初始化爬虫相关表
func initializeCrawlerTables(db *sql.DB) error {
	// 爬虫表的SQL定义
	crawlerTables := []string{
		// 1. 爬取目标表
		`CREATE TABLE IF NOT EXISTS crawl_targets (
			id INT AUTO_INCREMENT PRIMARY KEY,
			name VARCHAR(200) NOT NULL COMMENT '目标名称',
			url TEXT NOT NULL COMMENT '目标URL',
			type ENUM('website', 'api', 'rss', 'search_engine') DEFAULT 'website' COMMENT '目标类型',
			category VARCHAR(100) DEFAULT 'general' COMMENT '知识分类',
			keywords JSON COMMENT '关键词',
			selectors JSON COMMENT 'CSS选择器配置',
			filters JSON COMMENT '过滤条件',
			schedule VARCHAR(50) DEFAULT '0 */6 * * *' COMMENT '调度表达式',
			enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
			last_crawled TIMESTAMP NULL COMMENT '最后爬取时间',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_enabled (enabled),
			INDEX idx_type (type),
			INDEX idx_category (category),
			INDEX idx_last_crawled (last_crawled)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取目标表'`,

		// 2. 爬取结果表
		`CREATE TABLE IF NOT EXISTS crawl_results (
			id INT AUTO_INCREMENT PRIMARY KEY,
			target_id INT NOT NULL COMMENT '目标ID',
			url TEXT NOT NULL COMMENT '爬取URL',
			title VARCHAR(500) COMMENT '标题',
			content LONGTEXT COMMENT '内容',
			summary TEXT COMMENT '摘要',
			keywords JSON COMMENT '关键词',
			category VARCHAR(100) COMMENT '分类',
			metadata JSON COMMENT '元数据',
			crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '爬取时间',
			processed_at TIMESTAMP NULL COMMENT '处理时间',
			status ENUM('pending', 'processed', 'failed') DEFAULT 'pending' COMMENT '状态',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (target_id) REFERENCES crawl_targets(id) ON DELETE CASCADE,
			INDEX idx_target_id (target_id),
			INDEX idx_status (status),
			INDEX idx_crawled_at (crawled_at),
			INDEX idx_processed_at (processed_at),
			INDEX idx_category (category)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取结果表'`,

		// 3. 爬取日志表
		`CREATE TABLE IF NOT EXISTS crawl_logs (
			id INT AUTO_INCREMENT PRIMARY KEY,
			target_id INT NOT NULL COMMENT '目标ID',
			url TEXT COMMENT '爬取URL',
			status ENUM('success', 'failed', 'timeout', 'error') NOT NULL COMMENT '状态',
			message TEXT COMMENT '日志消息',
			error_details TEXT COMMENT '错误详情',
			duration_ms INT COMMENT '耗时(毫秒)',
			response_code INT COMMENT 'HTTP响应码',
			response_size INT COMMENT '响应大小(字节)',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (target_id) REFERENCES crawl_targets(id) ON DELETE CASCADE,
			INDEX idx_target_id (target_id),
			INDEX idx_status (status),
			INDEX idx_created_at (created_at)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取日志表'`,

		// 4. 爬虫配置表
		`CREATE TABLE IF NOT EXISTS crawler_config (
			id INT AUTO_INCREMENT PRIMARY KEY,
			config_key VARCHAR(100) NOT NULL COMMENT '配置键',
			config_value TEXT NOT NULL COMMENT '配置值',
			config_type ENUM('string', 'int', 'float', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
			description TEXT COMMENT '配置描述',
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			UNIQUE KEY uk_config_key (config_key)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬虫配置表'`,

		// 5. 爬取统计表
		`CREATE TABLE IF NOT EXISTS crawl_statistics (
			id INT AUTO_INCREMENT PRIMARY KEY,
			target_id INT NOT NULL COMMENT '目标ID',
			date DATE NOT NULL COMMENT '统计日期',
			total_crawls INT DEFAULT 0 COMMENT '总爬取次数',
			successful_crawls INT DEFAULT 0 COMMENT '成功爬取次数',
			failed_crawls INT DEFAULT 0 COMMENT '失败爬取次数',
			total_pages INT DEFAULT 0 COMMENT '总页面数',
			total_knowledge INT DEFAULT 0 COMMENT '提取知识数',
			avg_response_time INT DEFAULT 0 COMMENT '平均响应时间(ms)',
			total_data_size BIGINT DEFAULT 0 COMMENT '总数据大小(字节)',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			FOREIGN KEY (target_id) REFERENCES crawl_targets(id) ON DELETE CASCADE,
			UNIQUE KEY uk_target_date (target_id, date),
			INDEX idx_date (date)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取统计表'`,
	}

	// 创建表
	for i, tableSQL := range crawlerTables {
		if _, err := db.Exec(tableSQL); err != nil {
			logger.Errorf("创建爬虫表 %d 失败: %v", i+1, err)
			return err
		}
		logger.Infof("✅ 创建爬虫表 %d/%d", i+1, len(crawlerTables))
	}

	// 插入默认配置
	logger.Info("插入爬虫默认配置...")
	if err := insertCrawlerDefaultConfig(db); err != nil {
		logger.Warnf("插入爬虫默认配置失败: %v", err)
	} else {
		logger.Info("爬虫默认配置插入成功")
	}

	// 插入示例爬取目标
	logger.Info("插入示例爬取目标...")
	if err := insertSampleCrawlTargets(db); err != nil {
		logger.Warnf("插入示例爬取目标失败: %v", err)
	} else {
		logger.Info("示例爬取目标插入成功")
	}

	return nil
}

// insertCrawlerDefaultConfig 插入爬虫默认配置
func insertCrawlerDefaultConfig(db *sql.DB) error {
	configs := []string{
		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('max_concurrency', '5', 'int', '最大并发爬取数')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('request_delay', '2000', 'int', '请求间隔(毫秒)')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('timeout', '30000', 'int', '请求超时(毫秒)')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('user_agent', 'FAQ-System-Crawler/1.0', 'string', 'User-Agent')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('max_retries', '3', 'int', '最大重试次数')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('enable_javascript', 'false', 'boolean', '是否启用JavaScript渲染')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('max_content_length', '1048576', 'int', '最大内容长度(字节)')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('enable_robots_txt', 'true', 'boolean', '是否遵守robots.txt')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('crawl_depth', '3', 'int', '最大爬取深度')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('enable_duplicate_filter', 'true', 'boolean', '是否启用重复过滤')`,
	}

	for i, configSQL := range configs {
		if _, err := db.Exec(configSQL); err != nil {
			logger.Warnf("插入爬虫配置 %d 失败: %v", i+1, err)
		} else {
			logger.Infof("✅ 插入爬虫配置 %d/%d", i+1, len(configs))
		}
	}

	return nil
}

// insertSampleCrawlTargets 插入示例爬取目标
func insertSampleCrawlTargets(db *sql.DB) error {
	// 检查是否已有爬取目标
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM crawl_targets").Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		logger.Infof("爬取目标表中已有 %d 条数据，跳过示例数据插入", count)
		return nil
	}

	targets := []string{
		`INSERT IGNORE INTO crawl_targets (name, url, type, category, keywords, selectors, schedule, enabled) VALUES
		('测试API - 每分钟', 'https://httpbin.org/json', 'api', 'test',
		 '["test", "api", "json", "httpbin"]',
		 '{"title": "title", "content": "body"}',
		 '* * * * *', false)`,

		`INSERT IGNORE INTO crawl_targets (name, url, type, category, keywords, selectors, schedule, enabled) VALUES
		('Python官方文档', 'https://docs.python.org/3/', 'website', 'technology',
		 '["python", "documentation", "programming"]',
		 '{"title": "title", "content": ".body"}',
		 '0 0 * * *', false)`,

		`INSERT IGNORE INTO crawl_targets (name, url, type, category, keywords, selectors, schedule, enabled) VALUES
		('GitHub Trending', 'https://github.com/trending', 'website', 'technology',
		 '["github", "trending", "opensource"]',
		 '{"title": "h1", "content": ".Box-row"}',
		 '0 */6 * * *', false)`,

		`INSERT IGNORE INTO crawl_targets (name, url, type, category, keywords, selectors, schedule, enabled) VALUES
		('Stack Overflow Python', 'https://stackoverflow.com/questions/tagged/python', 'website', 'technology',
		 '["python", "stackoverflow", "qa"]',
		 '{"title": ".question-hyperlink", "content": ".post-text"}',
		 '0 */4 * * *', false)`,

		`INSERT IGNORE INTO crawl_targets (name, url, type, category, keywords, selectors, schedule, enabled) VALUES
		('MDN Web Docs', 'https://developer.mozilla.org/en-US/docs/Web/JavaScript', 'website', 'technology',
		 '["javascript", "mdn", "web", "documentation"]',
		 '{"title": "h1", "content": ".main-page-content"}',
		 '0 0 * * *', false)`,
	}

	for i, targetSQL := range targets {
		if _, err := db.Exec(targetSQL); err != nil {
			logger.Warnf("插入示例爬取目标 %d 失败: %v", i+1, err)
		} else {
			logger.Infof("✅ 插入示例爬取目标 %d/%d", i+1, len(targets))
		}
	}

	return nil
}

// printMySQLCharsetInfo 打印MySQL字符集配置信息
func printMySQLCharsetInfo(db *sql.DB) {
	logger.Info("=== MySQL字符集配置信息 ===")

	// 查询字符集变量
	charsetVars := []string{
		"character_set_client",
		"character_set_connection",
		"character_set_results",
	}

	for _, varName := range charsetVars {
		var variable, value string
		query := fmt.Sprintf("SHOW VARIABLES LIKE '%s'", varName)
		err := db.QueryRow(query).Scan(&variable, &value)
		if err != nil {
			logger.Warnf("查询 %s 失败: %v", varName, err)
			continue
		}
		logger.Infof("%-25s: %s", variable, value)
	}

	logger.Info("=== 字符集配置信息结束 ===")
}
