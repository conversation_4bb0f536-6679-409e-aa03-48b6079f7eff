package nlp

import (
	"log"
	"regexp"
	"sort"
	"strings"
	"unicode"
)

// ContentExtractor NLP内容提取器
type ContentExtractor struct {
	jiebaProcessor *JiebaProcessor
	stopWords      map[string]bool
	entityTypes    map[string][]string
	patterns       map[string]*regexp.Regexp
}

// ExtractedContent 提取的内容结构
type ExtractedContent struct {
	Title          string            `json:"title"`           // 智能提取的标题
	Summary        string            `json:"summary"`         // 智能摘要
	Keywords       []string          `json:"keywords"`        // 关键词列表
	Entities       []Entity          `json:"entities"`        // 命名实体
	Topics         []Topic           `json:"topics"`          // 主题话题
	ContentType    string            `json:"content_type"`    // 内容类型
	Sentiment      string            `json:"sentiment"`       // 情感倾向
	Confidence     float64           `json:"confidence"`      // 提取置信度
	StructuredData map[string]string `json:"structured_data"` // 结构化数据
}

// Entity 命名实体
type Entity struct {
	Text       string  `json:"text"`       // 实体文本
	Type       string  `json:"type"`       // 实体类型：PERSON, ORG, LOC, MISC
	Confidence float64 `json:"confidence"` // 置信度
	Position   int     `json:"position"`   // 在文本中的位置
}

// Topic 主题话题
type Topic struct {
	Title      string   `json:"title"`      // 话题标题
	Keywords   []string `json:"keywords"`   // 相关关键词
	Rank       int      `json:"rank"`       // 排名
	HotValue   string   `json:"hot_value"`  // 热度值
	Category   string   `json:"category"`   // 分类
	Confidence float64  `json:"confidence"` // 置信度
}

// NewContentExtractor 创建内容提取器
func NewContentExtractor() *ContentExtractor {
	log.Printf("🧠 初始化NLP内容提取器")

	extractor := &ContentExtractor{
		jiebaProcessor: NewJiebaProcessor(),
		stopWords:      make(map[string]bool),
		entityTypes:    make(map[string][]string),
		patterns:       make(map[string]*regexp.Regexp),
	}

	extractor.initializeStopWords()
	extractor.initializeEntityPatterns()
	extractor.initializeRegexPatterns()

	log.Printf("✅ NLP内容提取器初始化完成")
	return extractor
}

// ExtractContent 智能提取内容
func (ce *ContentExtractor) ExtractContent(rawContent, url, originalTitle string) *ExtractedContent {
	log.Printf("🧠 NLP内容提取开始")
	log.Printf("   原始标题: %s", originalTitle)
	log.Printf("   内容长度: %d", len(rawContent))
	log.Printf("   URL: %s", url)

	extracted := &ExtractedContent{
		StructuredData: make(map[string]string),
	}

	// 1. 预处理文本
	cleanContent := ce.preprocessText(rawContent)
	log.Printf("   预处理后长度: %d", len(cleanContent))

	// 2. 智能标题提取
	extracted.Title = ce.extractIntelligentTitle(cleanContent, originalTitle, url)
	log.Printf("   智能标题: %s", extracted.Title)

	// 3. 命名实体识别（使用gojieba）
	extracted.Entities = ce.jiebaProcessor.ExtractEntities(cleanContent)
	log.Printf("   提取实体: %d 个", len(extracted.Entities))

	// 4. 关键词提取（使用gojieba TF-IDF）
	keywordInfos := ce.jiebaProcessor.ExtractKeywords(cleanContent, 10)
	extracted.Keywords = ce.extractKeywordStrings(keywordInfos)
	log.Printf("   提取关键词: %d 个", len(extracted.Keywords))

	// 5. 主题话题提取（使用gojieba）
	extracted.Topics = ce.jiebaProcessor.ExtractTopics(cleanContent, 15)
	log.Printf("   提取话题: %d 个", len(extracted.Topics))

	// 6. 内容分类
	extracted.ContentType = ce.classifyContent(cleanContent, url)
	log.Printf("   内容类型: %s", extracted.ContentType)

	// 7. 情感分析
	extracted.Sentiment = ce.analyzeSentiment(cleanContent)
	log.Printf("   情感倾向: %s", extracted.Sentiment)

	// 8. 智能摘要生成
	extracted.Summary = ce.generateSummary(cleanContent, extracted.Topics)
	log.Printf("   摘要长度: %d", len(extracted.Summary))

	// 9. 计算置信度
	extracted.Confidence = ce.calculateConfidence(extracted)
	log.Printf("   提取置信度: %.2f", extracted.Confidence)

	// 10. 提取结构化数据
	extracted.StructuredData = ce.extractStructuredData(cleanContent, url)
	log.Printf("   结构化数据: %d 项", len(extracted.StructuredData))

	return extracted
}

// preprocessText 预处理文本
func (ce *ContentExtractor) preprocessText(content string) string {
	// 1. 移除HTML标签
	htmlRegex := regexp.MustCompile(`<[^>]*>`)
	content = htmlRegex.ReplaceAllString(content, "")

	// 2. 移除多余空白
	spaceRegex := regexp.MustCompile(`\s+`)
	content = spaceRegex.ReplaceAllString(content, " ")

	// 3. 移除特殊字符
	content = strings.ReplaceAll(content, "\u00a0", " ") // 非断行空格
	content = strings.ReplaceAll(content, "\u200b", "")  // 零宽空格

	// 4. 移除导航和广告文本
	filterPatterns := []string{
		"登录", "注册", "首页", "导航", "菜单", "广告", "推广",
		"更多", "展开", "收起", "点击", "链接", "跳转",
		"百度首页", "设置", "产品", "公司", "关于", "帮助",
		"javascript", "function", "var ", "document",
	}

	lines := strings.Split(content, "\n")
	cleanLines := []string{}

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) < 3 || len(line) > 200 {
			continue
		}

		isFilter := false
		for _, pattern := range filterPatterns {
			if strings.Contains(strings.ToLower(line), strings.ToLower(pattern)) {
				isFilter = true
				break
			}
		}

		if !isFilter {
			cleanLines = append(cleanLines, line)
		}
	}

	return strings.Join(cleanLines, "\n")
}

// extractIntelligentTitle 智能标题提取
func (ce *ContentExtractor) extractIntelligentTitle(content, originalTitle, url string) string {
	// 1. 如果原标题有意义，优先使用
	if ce.isMeaningfulTitle(originalTitle) {
		return ce.cleanTitle(originalTitle)
	}

	// 2. 从URL中提取标题信息
	if urlTitle := ce.extractTitleFromURL(url); urlTitle != "" {
		return urlTitle
	}

	// 3. 从内容中提取最可能的标题
	return ce.extractTitleFromContent(content)
}

// isMeaningfulTitle 判断标题是否有意义
func (ce *ContentExtractor) isMeaningfulTitle(title string) bool {
	if len(title) < 3 || len(title) > 100 {
		return false
	}

	// 过滤无意义的标题
	meaninglessPatterns := []string{
		"百度一下", "百度首页", "登录", "注册", "404", "错误",
		"加载中", "请稍候", "网页", "页面", "document", "untitled",
	}

	titleLower := strings.ToLower(title)
	for _, pattern := range meaninglessPatterns {
		if strings.Contains(titleLower, strings.ToLower(pattern)) {
			return false
		}
	}

	return true
}

// cleanTitle 清理标题
func (ce *ContentExtractor) cleanTitle(title string) string {
	// 移除常见的网站后缀
	suffixes := []string{
		"_百度搜索", " - 百度", "百度", " | ", " - ", " _ ",
		"官网", "首页", "主页", "网站",
	}

	for _, suffix := range suffixes {
		title = strings.ReplaceAll(title, suffix, "")
	}

	return strings.TrimSpace(title)
}

// extractTitleFromURL 从URL提取标题
func (ce *ContentExtractor) extractTitleFromURL(url string) string {
	// 百度热搜特殊处理
	if strings.Contains(url, "top.baidu.com") {
		if strings.Contains(url, "tab=realtime") {
			return "实时热点榜"
		}
		if strings.Contains(url, "tab=novel") {
			return "小说热搜榜"
		}
		if strings.Contains(url, "tab=movie") {
			return "电影热搜榜"
		}
		if strings.Contains(url, "tab=teleplay") {
			return "电视剧热搜榜"
		}
		if strings.Contains(url, "tab=homepage") {
			return "综合热搜榜"
		}
		return "百度热搜榜"
	}

	return ""
}

// extractTitleFromContent 从内容提取标题
func (ce *ContentExtractor) extractTitleFromContent(content string) string {
	lines := strings.Split(content, "\n")

	// 查找最可能的标题行
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) >= 5 && len(line) <= 50 {
			// 检查是否包含标题特征
			if ce.hasTitleCharacteristics(line) {
				return line
			}
		}
	}

	// 如果没找到，返回第一行有意义的内容
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) >= 5 && len(line) <= 30 {
			return line
		}
	}

	return "热门内容"
}

// hasTitleCharacteristics 检查是否具有标题特征
func (ce *ContentExtractor) hasTitleCharacteristics(text string) bool {
	// 标题通常包含的特征
	titleFeatures := []string{
		"榜", "排行", "热搜", "热点", "最新", "今日", "当前",
		"小说", "电影", "电视剧", "新闻", "话题", "事件",
	}

	for _, feature := range titleFeatures {
		if strings.Contains(text, feature) {
			return true
		}
	}

	// 检查是否主要由中文组成
	chineseCount := 0
	totalCount := 0
	for _, r := range text {
		totalCount++
		if unicode.Is(unicode.Han, r) {
			chineseCount++
		}
	}

	return float64(chineseCount)/float64(totalCount) > 0.7
}

// initializeStopWords 初始化停用词
func (ce *ContentExtractor) initializeStopWords() {
	stopWordsList := []string{
		"的", "了", "在", "是", "我", "有", "和", "就", "不", "人",
		"都", "一", "一个", "上", "也", "很", "到", "说", "要", "去",
		"你", "会", "着", "没有", "看", "好", "自己", "这", "那", "什么",
		"为", "以", "时", "来", "用", "们", "生", "对", "作", "地",
		"于", "出", "分", "可", "下", "又", "后", "同", "现", "或",
		"百度", "搜索", "首页", "登录", "注册", "更多", "展开", "收起",
		"点击", "链接", "跳转", "网站", "页面", "浏览器", "加载",
	}

	for _, word := range stopWordsList {
		ce.stopWords[word] = true
	}
}

// initializeEntityPatterns 初始化实体模式
func (ce *ContentExtractor) initializeEntityPatterns() {
	// 人名模式
	ce.entityTypes["PERSON"] = []string{
		"导演", "主演", "演员", "作者", "编剧", "制片人", "歌手", "明星",
		"总统", "主席", "部长", "市长", "教授", "博士", "先生", "女士",
	}

	// 机构模式
	ce.entityTypes["ORG"] = []string{
		"公司", "集团", "企业", "机构", "组织", "协会", "基金会", "学院",
		"大学", "医院", "银行", "政府", "部门", "委员会", "研究所",
	}

	// 地点模式
	ce.entityTypes["LOC"] = []string{
		"市", "省", "县", "区", "镇", "村", "街道", "路", "国", "州",
		"北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都",
	}

	// 其他实体模式
	ce.entityTypes["MISC"] = []string{
		"电影", "电视剧", "小说", "游戏", "软件", "产品", "品牌", "活动",
		"比赛", "赛事", "节目", "演出", "展览", "会议", "论坛", "峰会",
	}
}

// initializeRegexPatterns 初始化正则模式
func (ce *ContentExtractor) initializeRegexPatterns() {
	// 热度值模式
	ce.patterns["hot_value"] = regexp.MustCompile(`(\d+\.?\d*万|\d{4,})`)

	// 排名模式
	ce.patterns["rank"] = regexp.MustCompile(`第?(\d+)名?位?`)

	// 时间模式
	ce.patterns["time"] = regexp.MustCompile(`(\d{4}年\d{1,2}月\d{1,2}日|\d{1,2}月\d{1,2}日|\d{1,2}:\d{2})`)

	// 数字模式
	ce.patterns["number"] = regexp.MustCompile(`(\d+)`)
}

// extractEntities 命名实体识别
func (ce *ContentExtractor) extractEntities(content string) []Entity {
	entities := []Entity{}

	// 基于规则的实体识别
	for entityType, patterns := range ce.entityTypes {
		for _, pattern := range patterns {
			if strings.Contains(content, pattern) {
				// 查找包含该模式的句子
				sentences := ce.splitIntoSentences(content)
				for _, sentence := range sentences {
					if strings.Contains(sentence, pattern) {
						entity := ce.extractEntityFromSentence(sentence, pattern, entityType)
						if entity != nil {
							entities = append(entities, *entity)
						}
					}
				}
			}
		}
	}

	// 去重并排序
	entities = ce.deduplicateEntities(entities)

	return entities[:ce.min(len(entities), 10)] // 最多返回10个实体
}

// extractEntityFromSentence 从句子中提取实体
func (ce *ContentExtractor) extractEntityFromSentence(sentence, pattern, entityType string) *Entity {
	// 简单的实体提取逻辑
	words := strings.Fields(sentence)
	for i, word := range words {
		if strings.Contains(word, pattern) {
			// 尝试提取实体名称
			if i > 0 {
				entityText := words[i-1]
				if len(entityText) >= 2 && len(entityText) <= 20 {
					return &Entity{
						Text:       entityText,
						Type:       entityType,
						Confidence: 0.7,
						Position:   i - 1,
					}
				}
			}
		}
	}
	return nil
}

// extractKeywords 关键词提取
func (ce *ContentExtractor) extractKeywords(content string) []string {
	// 1. 分词
	words := ce.tokenize(content)

	// 2. 计算词频
	wordFreq := make(map[string]int)
	for _, word := range words {
		if !ce.stopWords[word] && len(word) >= 2 {
			wordFreq[word]++
		}
	}

	// 3. 按频率排序
	type wordScore struct {
		word  string
		score int
	}

	var scores []wordScore
	for word, freq := range wordFreq {
		scores = append(scores, wordScore{word, freq})
	}

	sort.Slice(scores, func(i, j int) bool {
		return scores[i].score > scores[j].score
	})

	// 4. 返回前10个关键词
	keywords := []string{}
	for i, score := range scores {
		if i >= 10 {
			break
		}
		keywords = append(keywords, score.word)
	}

	return keywords
}

// extractTopics 主题话题提取
func (ce *ContentExtractor) extractTopics(content, url string) []Topic {
	topics := []Topic{}

	// 根据URL类型采用不同的话题提取策略
	if strings.Contains(url, "top.baidu.com") {
		topics = ce.extractBaiduTopics(content)
	} else {
		topics = ce.extractGeneralTopics(content)
	}

	return topics
}

// extractBaiduTopics 提取百度热搜话题
func (ce *ContentExtractor) extractBaiduTopics(content string) []Topic {
	topics := []Topic{}
	lines := strings.Split(content, "\n")

	rank := 1
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 跳过太短或太长的行
		if len(line) < 5 || len(line) > 100 {
			continue
		}

		// 检查是否为话题行
		if ce.isTopicLine(line) {
			topic := Topic{
				Title:      ce.cleanTopicTitle(line),
				Rank:       rank,
				HotValue:   ce.extractHotValue(line),
				Category:   ce.classifyTopic(line),
				Confidence: ce.calculateTopicConfidence(line),
				Keywords:   ce.extractTopicKeywords(line),
			}

			if topic.Confidence > 0.5 {
				topics = append(topics, topic)
				rank++

				if len(topics) >= 15 {
					break
				}
			}
		}
	}

	return topics
}

// isTopicLine 判断是否为话题行
func (ce *ContentExtractor) isTopicLine(line string) bool {
	// 包含热度指标
	if ce.patterns["hot_value"].MatchString(line) {
		return true
	}

	// 包含话题特征词
	topicFeatures := []string{
		"热", "新", "沸", "爆", "火", "榜", "排行",
		"明星", "电影", "电视剧", "小说", "游戏", "新闻",
	}

	for _, feature := range topicFeatures {
		if strings.Contains(line, feature) {
			return true
		}
	}

	// 长度适中且主要为中文
	if len(line) >= 8 && len(line) <= 50 {
		chineseRatio := ce.calculateChineseRatio(line)
		return chineseRatio > 0.6
	}

	return false
}

// cleanTopicTitle 清理话题标题
func (ce *ContentExtractor) cleanTopicTitle(title string) string {
	// 移除热度值
	title = ce.patterns["hot_value"].ReplaceAllString(title, "")

	// 移除排名信息
	title = ce.patterns["rank"].ReplaceAllString(title, "")

	// 移除特殊字符
	title = strings.ReplaceAll(title, "热", "")
	title = strings.ReplaceAll(title, "新", "")
	title = strings.ReplaceAll(title, "沸", "")

	return strings.TrimSpace(title)
}

// extractHotValue 提取热度值
func (ce *ContentExtractor) extractHotValue(text string) string {
	matches := ce.patterns["hot_value"].FindStringSubmatch(text)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// classifyTopic 话题分类
func (ce *ContentExtractor) classifyTopic(text string) string {
	categories := map[string][]string{
		"娱乐": {"明星", "演员", "歌手", "综艺", "娱乐"},
		"影视": {"电影", "电视剧", "导演", "主演", "上映", "播出"},
		"文学": {"小说", "作者", "作品", "文学", "书籍"},
		"科技": {"科技", "技术", "AI", "互联网", "数码", "手机"},
		"体育": {"体育", "比赛", "运动", "球员", "赛事", "奥运"},
		"社会": {"社会", "新闻", "事件", "政策", "民生"},
		"财经": {"经济", "股票", "金融", "投资", "市场", "公司"},
	}

	for category, keywords := range categories {
		for _, keyword := range keywords {
			if strings.Contains(text, keyword) {
				return category
			}
		}
	}

	return "综合"
}

// calculateTopicConfidence 计算话题置信度
func (ce *ContentExtractor) calculateTopicConfidence(text string) float64 {
	confidence := 0.0

	// 长度评分
	if len(text) >= 10 && len(text) <= 30 {
		confidence += 0.3
	} else if len(text) >= 5 && len(text) <= 50 {
		confidence += 0.2
	}

	// 中文比例评分
	chineseRatio := ce.calculateChineseRatio(text)
	confidence += chineseRatio * 0.3

	// 热度值评分
	if ce.patterns["hot_value"].MatchString(text) {
		confidence += 0.2
	}

	// 话题特征评分
	topicFeatures := []string{"热", "新", "沸", "爆", "火"}
	for _, feature := range topicFeatures {
		if strings.Contains(text, feature) {
			confidence += 0.1
			break
		}
	}

	return ce.min(confidence, 1.0)
}

// extractTopicKeywords 提取话题关键词
func (ce *ContentExtractor) extractTopicKeywords(text string) []string {
	words := ce.tokenize(text)
	keywords := []string{}

	for _, word := range words {
		if !ce.stopWords[word] && len(word) >= 2 && len(word) <= 10 {
			keywords = append(keywords, word)
		}
	}

	return keywords[:ce.min(len(keywords), 5)]
}
