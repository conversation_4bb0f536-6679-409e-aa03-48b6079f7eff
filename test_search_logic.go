package main

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接数据库
	db, err := sql.Open("mysql", "root:park%123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 测试FAQ系统搜索逻辑...")

	// 测试查询
	testQueries := []string{
		"百宝箱",
		"热搜",
		"百度热搜",
		"热门话题",
		"微信",
	}

	for _, query := range testQueries {
		fmt.Printf("\n🔍 测试查询: '%s'\n", query)
		
		// 1. 模拟fallbackTextSearch的逻辑
		fmt.Println("📚 模拟学习知识搜索:")
		testFallbackSearch(db, query)
		
		// 2. 检查数据库中的实际内容
		fmt.Println("🔍 检查数据库实际内容:")
		checkActualContent(db, query)
	}

	// 3. 检查学习知识的状态分布
	fmt.Println("\n📊 学习知识状态分布:")
	checkKnowledgeStatus(db)

	// 4. 尝试将pending状态改为approved
	fmt.Println("\n🔄 将热搜相关知识状态改为approved:")
	updateHotSearchStatus(db)
}

func testFallbackSearch(db *sql.DB, query string) {
	// 模拟fallbackTextSearch的查询逻辑
	searchQuery := `
		SELECT id, question, answer, source, confidence, category,
		       keywords, context, learned_from, status, created_at
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY confidence DESC, created_at DESC
		LIMIT ?
	`

	rows, err := db.Query(searchQuery, 15) // 获取更多结果用于过滤
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	var allResults []map[string]interface{}
	for rows.Next() {
		var id int
		var question, answer, source, category, keywords, context, learnedFrom, status, createdAt string
		var confidence float32

		err := rows.Scan(&id, &question, &answer, &source, &confidence, &category,
			&keywords, &context, &learnedFrom, &status, &createdAt)
		if err != nil {
			continue
		}

		result := map[string]interface{}{
			"id":           id,
			"question":     question,
			"answer":       answer,
			"source":       source,
			"confidence":   confidence,
			"category":     category,
			"keywords":     keywords,
			"context":      context,
			"learned_from": learnedFrom,
			"status":       status,
			"created_at":   createdAt,
		}
		allResults = append(allResults, result)
	}

	fmt.Printf("  📊 获取到 %d 条学习知识\n", len(allResults))

	// 在应用层进行文本匹配过滤
	var matchedResults []map[string]interface{}
	queryLower := strings.ToLower(query)

	for _, result := range allResults {
		question := result["question"].(string)
		answer := result["answer"].(string)
		
		questionLower := strings.ToLower(question)
		answerLower := strings.ToLower(answer)

		if strings.Contains(questionLower, queryLower) || strings.Contains(answerLower, queryLower) {
			matchedResults = append(matchedResults, result)
			if len(matchedResults) >= 3 {
				break
			}
		}
	}

	if len(matchedResults) == 0 {
		fmt.Println("  ❌ 未找到匹配结果")
	} else {
		fmt.Printf("  ✅ 找到 %d 个匹配结果:\n", len(matchedResults))
		for i, result := range matchedResults {
			fmt.Printf("    %d. ID %d (状态: %s, 置信度: %.2f): %s\n", 
				i+1, result["id"].(int), result["status"].(string), 
				result["confidence"].(float32), result["question"].(string))
			
			answer := result["answer"].(string)
			if len(answer) > 100 {
				answer = answer[:100] + "..."
			}
			fmt.Printf("       答案: %s\n", answer)
		}
	}
}

func checkActualContent(db *sql.DB, query string) {
	// 直接检查数据库中包含查询词的内容
	searchQuery := `
		SELECT id, question, answer, status, confidence
		FROM learned_knowledge
		WHERE question LIKE CONCAT('%', ?, '%') OR answer LIKE CONCAT('%', ?, '%')
		ORDER BY confidence DESC, created_at DESC
		LIMIT 3
	`

	rows, err := db.Query(searchQuery, query, query)
	if err != nil {
		fmt.Printf("❌ 直接搜索失败: %v\n", err)
		return
	}
	defer rows.Close()

	found := false
	for rows.Next() {
		var id int
		var question, answer, status string
		var confidence float32

		err := rows.Scan(&id, &question, &answer, &status, &confidence)
		if err != nil {
			continue
		}

		if !found {
			fmt.Println("  ✅ 直接搜索结果:")
			found = true
		}

		answerPreview := answer
		if len(answerPreview) > 100 {
			answerPreview = answerPreview[:100] + "..."
		}

		fmt.Printf("    ID %d (状态: %s, 置信度: %.2f): %s\n", id, status, confidence, question)
		fmt.Printf("    答案: %s\n", answerPreview)
	}

	if !found {
		fmt.Println("  ❌ 直接搜索未找到结果")
	}
}

func checkKnowledgeStatus(db *sql.DB) {
	statusQuery := `
		SELECT status, COUNT(*) as count
		FROM learned_knowledge
		GROUP BY status
		ORDER BY count DESC
	`

	rows, err := db.Query(statusQuery)
	if err != nil {
		fmt.Printf("❌ 查询状态分布失败: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var status string
		var count int

		err := rows.Scan(&status, &count)
		if err != nil {
			continue
		}

		fmt.Printf("  %s: %d 条\n", status, count)
	}
}

func updateHotSearchStatus(db *sql.DB) {
	// 将包含热搜相关内容的知识状态改为approved
	updateQuery := `
		UPDATE learned_knowledge 
		SET status = 'approved' 
		WHERE (question LIKE '%热搜%' OR answer LIKE '%热搜%' OR question LIKE '%热门话题%' OR answer LIKE '%热门话题%')
		AND status = 'pending'
	`

	result, err := db.Exec(updateQuery)
	if err != nil {
		fmt.Printf("❌ 更新状态失败: %v\n", err)
		return
	}

	rowsAffected, _ := result.RowsAffected()
	fmt.Printf("✅ 成功更新 %d 条记录的状态为approved\n", rowsAffected)
}
