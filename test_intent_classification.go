package main

import (
	"fmt"
	"strings"
)

// 模拟意图分类器
type IntentClassifier struct {
	patterns map[string][]string
}

func NewIntentClassifier() *IntentClassifier {
	patterns := map[string][]string{
		"greeting": {
			"你好", "hello", "hi", "嗨", "您好", "早上好", "下午好", "晚上好",
			"大家好", "各位好", "小伙伴们好", "朋友们好",
		},
		"identity_inquiry": {
			"你是谁", "你是什么", "你叫什么", "介绍一下自己", "你的身份", "你是",
		},
		"technical_question": {
			"什么是", "如何", "怎么", "为什么", "原理", "实现", "配置", "安装", "部署",
			"localai", "mysql", "数据库", "向量", "embedding", "go语言", "golang",
			"搜索", "系统", "架构", "开发", "编程", "技术", "代码", "框架", "工具",
		},
	}

	return &IntentClassifier{patterns: patterns}
}

func (ic *IntentClassifier) ClassifyIntent(query string) string {
	query = strings.ToLower(strings.TrimSpace(query))

	// 对于简单查询（单个技术词汇或过短查询），更谨慎处理
	isSimpleQuery := ic.isSimpleQuery(query)

	// 计算每个意图的匹配分数
	scores := make(map[string]float32)

	for intent, patterns := range ic.patterns {
		score := float32(0)
		for _, pattern := range patterns {
			if strings.Contains(query, pattern) {
				// 完全匹配得分更高
				if query == pattern {
					score += 2.0
				} else {
					score += 1.0
				}
			}
		}

		// 对简单查询进行特殊处理
		if isSimpleQuery && intent == "technical_question" {
			// 简单查询需要更强的技术问题特征才能被识别为技术问题
			if !ic.hasStrongTechnicalIndicators(query) {
				score *= 0.1 // 大幅降低简单查询的技术问题得分
			}
		}

		scores[intent] = score
	}

	// 找到最高分的意图
	maxScore := float32(0)
	bestIntent := "general"

	for intent, score := range scores {
		if score > maxScore {
			maxScore = score
			bestIntent = intent
		}
	}

	// 最后检查：如果是简单查询但被识别为技术问题，强制改为general
	if isSimpleQuery && bestIntent == "technical_question" && !ic.hasStrongTechnicalIndicators(query) {
		bestIntent = "general"
	}

	return bestIntent
}

func (ic *IntentClassifier) isSimpleQuery(query string) bool {
	query = strings.TrimSpace(query)
	queryLower := strings.ToLower(query)

	// 过短的查询（少于3个字符）
	if len(strings.ReplaceAll(query, " ", "")) < 3 {
		return true
	}

	// 单个技术词汇列表
	singleTechWords := []string{
		"go", "golang", "mysql", "localai", "embedding", "vector", "rag",
		"java", "python", "javascript", "react", "vue", "node", "docker",
		"redis", "mongodb", "postgresql", "nginx", "apache", "linux",
		"windows", "macos", "git", "github", "gitlab", "kubernetes",
	}

	// 检查是否是单个技术词汇
	for _, word := range singleTechWords {
		if queryLower == word {
			return true
		}
	}

	return false
}

func (ic *IntentClassifier) hasStrongTechnicalIndicators(query string) bool {
	// 强技术问题指示词 - 这些词汇明确表示用户在询问技术问题
	strongIndicators := []string{
		"什么是", "如何", "怎么", "为什么", "原理", "实现", "配置", "安装", "部署",
		"介绍", "解释", "说明", "教程", "文档", "使用方法", "操作步骤",
		"what", "how", "why", "explain", "tutorial", "documentation",
	}

	for _, indicator := range strongIndicators {
		if strings.Contains(query, indicator) {
			return true
		}
	}

	return false
}

func main() {
	fmt.Println("🔍 测试意图分类...")

	classifier := NewIntentClassifier()

	testQueries := []string{
		"百宝箱",
		"热搜",
		"百度热搜",
		"当前热门话题",
		"微信",
		"什么是百度热搜",
		"如何查看热搜",
		"热搜榜",
		"热门话题有哪些",
		"最新热搜",
	}

	for _, query := range testQueries {
		intent := classifier.ClassifyIntent(query)
		isSimple := classifier.isSimpleQuery(query)
		hasStrong := classifier.hasStrongTechnicalIndicators(query)

		fmt.Printf("查询: '%s'\n", query)
		fmt.Printf("  意图: %s\n", intent)
		fmt.Printf("  简单查询: %t\n", isSimple)
		fmt.Printf("  强技术指示词: %t\n", hasStrong)
		fmt.Println()
	}

	fmt.Println("🔍 分析结果:")
	fmt.Println("- '百度热搜'等查询被分类为'general'意图")
	fmt.Println("- 这意味着会调用handleGeneral函数")
	fmt.Println("- handleGeneral函数会搜索学习知识")
	fmt.Println("- 但可能存在其他问题导致搜索失败")
}
