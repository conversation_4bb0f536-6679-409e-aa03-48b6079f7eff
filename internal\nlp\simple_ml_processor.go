package nlp

import (
	"log"
	"math"
	"sort"
	"strings"
)

// SimpleMLProcessor 简化的机器学习处理器
type SimpleMLProcessor struct {
	initialized bool
	vocabulary  map[string]int
	categories  []string
}

// MLResult 机器学习处理结果
type MLResult struct {
	Embeddings      [][]float64           `json:"embeddings"`       // 词向量
	Classifications []ClassificationInfo `json:"classifications"`  // 分类结果
	SentimentScore  float64              `json:"sentiment_score"`  // 情感分数
	SentimentLabel  string               `json:"sentiment_label"`  // 情感标签
	Confidence      float64              `json:"confidence"`       // 置信度
	Features        map[string]float64   `json:"features"`         // 特征向量
}

// NewSimpleMLProcessor 创建简化ML处理器
func NewSimpleMLProcessor() *SimpleMLProcessor {
	log.Printf("🤖 初始化简化机器学习处理器")
	
	processor := &SimpleMLProcessor{
		initialized: false,
		vocabulary:  make(map[string]int),
		categories: []string{
			"娱乐", "科技", "体育", "财经", "社会", "文学", "影视", "综合",
		},
	}
	
	// 异步初始化
	go processor.initializeAsync()
	
	return processor
}

// initializeAsync 异步初始化
func (smp *SimpleMLProcessor) initializeAsync() {
	log.Printf("🔧 开始初始化ML组件...")
	
	// 初始化词汇表
	smp.initializeVocabulary()
	
	smp.initialized = true
	log.Printf("✅ 简化ML处理器初始化完成")
}

// ProcessText 处理文本
func (smp *SimpleMLProcessor) ProcessText(text string) *MLResult {
	if !smp.initialized {
		log.Printf("⚠️ ML处理器未完全初始化，使用基础处理")
		return smp.basicProcess(text)
	}
	
	log.Printf("🤖 ML处理文本: %s", text[:min(len(text), 50)])
	
	result := &MLResult{
		Features: make(map[string]float64),
	}
	
	// 1. 分词
	tokens := smp.tokenizeText(text)
	log.Printf("   分词结果: %d 个token", len(tokens))
	
	// 2. 生成词向量
	embeddings := smp.generateEmbeddings(tokens)
	result.Embeddings = embeddings
	log.Printf("   生成词向量: %d 个", len(embeddings))
	
	// 3. 文本分类
	classifications := smp.classifyText(embeddings, text)
	result.Classifications = classifications
	log.Printf("   分类结果: %d 个类别", len(classifications))
	
	// 4. 情感分析
	sentimentScore, sentimentLabel := smp.analyzeSentiment(embeddings, text)
	result.SentimentScore = sentimentScore
	result.SentimentLabel = sentimentLabel
	log.Printf("   情感分析: %s (%.2f)", sentimentLabel, sentimentScore)
	
	// 5. 计算置信度
	result.Confidence = smp.calculateConfidence(result)
	log.Printf("   处理置信度: %.2f", result.Confidence)
	
	// 6. 提取特征
	result.Features = smp.extractFeatures(text, embeddings)
	log.Printf("   提取特征: %d 个", len(result.Features))
	
	return result
}

// initializeVocabulary 初始化词汇表
func (smp *SimpleMLProcessor) initializeVocabulary() {
	// 基础词汇表
	commonWords := []string{
		"热搜", "排行", "榜单", "话题", "事件", "新闻", "消息",
		"明星", "演员", "歌手", "导演", "主演", "艺人", "偶像",
		"电影", "电视剧", "影片", "剧集", "上映", "播出", "首映",
		"小说", "作者", "作品", "文学", "书籍", "写作", "创作",
		"科技", "技术", "AI", "互联网", "数码", "手机", "电脑",
		"体育", "比赛", "运动", "球员", "赛事", "足球", "篮球",
		"经济", "股票", "金融", "投资", "市场", "公司", "企业",
		"社会", "政策", "民生", "国家", "政府", "公共", "服务",
	}
	
	for i, word := range commonWords {
		smp.vocabulary[word] = i
	}
	
	log.Printf("   词汇表初始化完成: %d 个词汇", len(smp.vocabulary))
}

// tokenizeText 分词
func (smp *SimpleMLProcessor) tokenizeText(text string) []string {
	// 简单分词
	tokens := strings.Fields(text)
	
	// 过滤和清理
	cleanTokens := []string{}
	for _, token := range tokens {
		token = strings.TrimSpace(token)
		if len(token) >= 2 && len(token) <= 20 {
			cleanTokens = append(cleanTokens, token)
		}
	}
	
	return cleanTokens
}

// generateEmbeddings 生成词向量
func (smp *SimpleMLProcessor) generateEmbeddings(tokens []string) [][]float64 {
	embeddings := [][]float64{}
	
	for _, token := range tokens {
		embedding := smp.getTokenEmbedding(token)
		embeddings = append(embeddings, embedding)
	}
	
	return embeddings
}

// getTokenEmbedding 获取词向量
func (smp *SimpleMLProcessor) getTokenEmbedding(token string) []float64 {
	// 简单的词向量生成（基于字符哈希）
	embedding := make([]float64, 50) // 50维词向量
	
	hash := 0
	for _, r := range token {
		hash = hash*31 + int(r)
	}
	
	// 基于词汇表的权重调整
	if idx, exists := smp.vocabulary[token]; exists {
		hash += idx * 1000 // 增加已知词汇的权重
	}
	
	for i := range embedding {
		embedding[i] = math.Sin(float64(hash+i)) * 0.1
	}
	
	return embedding
}

// classifyText 文本分类
func (smp *SimpleMLProcessor) classifyText(embeddings [][]float64, text string) []ClassificationInfo {
	classifications := []ClassificationInfo{}
	
	// 基于关键词的分类
	for i, category := range smp.categories {
		score := smp.calculateCategoryScore(embeddings, text, category)
		classifications = append(classifications, ClassificationInfo{
			Label:      category,
			Score:      score,
			Confidence: score * 0.8,
		})
	}
	
	// 按分数排序
	sort.Slice(classifications, func(i, j int) bool {
		return classifications[i].Score > classifications[j].Score
	})
	
	return classifications
}

// calculateCategoryScore 计算分类分数
func (smp *SimpleMLProcessor) calculateCategoryScore(embeddings [][]float64, text, category string) float64 {
	score := 0.0
	
	// 基于关键词匹配
	categoryKeywords := smp.getCategoryKeywords(category)
	for _, keyword := range categoryKeywords {
		if strings.Contains(text, keyword) {
			score += 1.0
		}
	}
	
	// 基于词向量相似度（简化计算）
	if len(embeddings) > 0 {
		avgEmbedding := smp.calculateAverageEmbedding(embeddings)
		categoryEmbedding := smp.getTokenEmbedding(category)
		similarity := smp.calculateCosineSimilarity(avgEmbedding, categoryEmbedding)
		score += similarity * 0.5
	}
	
	return score / float64(len(categoryKeywords)+1)
}

// getCategoryKeywords 获取分类关键词
func (smp *SimpleMLProcessor) getCategoryKeywords(category string) []string {
	keywordMap := map[string][]string{
		"娱乐": {"明星", "演员", "歌手", "综艺", "娱乐", "偶像", "艺人"},
		"科技": {"科技", "技术", "AI", "互联网", "数码", "手机", "电脑", "软件"},
		"体育": {"体育", "比赛", "运动", "球员", "赛事", "足球", "篮球", "奥运"},
		"财经": {"经济", "股票", "金融", "投资", "市场", "公司", "企业", "商业"},
		"社会": {"社会", "新闻", "事件", "政策", "民生", "国家", "政府", "公共"},
		"文学": {"小说", "作者", "作品", "文学", "书籍", "写作", "创作", "出版"},
		"影视": {"电影", "电视剧", "导演", "主演", "上映", "播出", "影片", "剧集"},
		"综合": {"热搜", "排行", "榜单", "话题", "热点", "流行", "趋势", "关注"},
	}
	
	if keywords, exists := keywordMap[category]; exists {
		return keywords
	}
	return []string{}
}

// analyzeSentiment 情感分析
func (smp *SimpleMLProcessor) analyzeSentiment(embeddings [][]float64, text string) (float64, string) {
	score := 0.5 // 默认中性
	
	// 基于情感词典
	positiveWords := []string{"好", "棒", "优秀", "精彩", "成功", "喜欢", "爱", "开心", "满意", "赞"}
	negativeWords := []string{"坏", "差", "失败", "糟糕", "讨厌", "恨", "愤怒", "失望", "不满", "批评"}
	
	positiveCount := 0
	negativeCount := 0
	
	for _, word := range positiveWords {
		positiveCount += strings.Count(text, word)
	}
	
	for _, word := range negativeWords {
		negativeCount += strings.Count(text, word)
	}
	
	total := positiveCount + negativeCount
	if total > 0 {
		score = float64(positiveCount) / float64(total)
	}
	
	// 基于词向量（简化计算）
	if len(embeddings) > 0 {
		avgEmbedding := smp.calculateAverageEmbedding(embeddings)
		if len(avgEmbedding) > 0 {
			// 使用第一个维度作为情感指标
			sentimentIndicator := (avgEmbedding[0] + 1) / 2 // 归一化到[0,1]
			score = (score + sentimentIndicator) / 2
		}
	}
	
	var label string
	if score > 0.6 {
		label = "积极"
	} else if score < 0.4 {
		label = "消极"
	} else {
		label = "中性"
	}
	
	return score, label
}

// calculateConfidence 计算置信度
func (smp *SimpleMLProcessor) calculateConfidence(result *MLResult) float64 {
	confidence := 0.0
	
	// 基于分类结果的置信度
	if len(result.Classifications) > 0 {
		confidence += result.Classifications[0].Confidence * 0.4
	}
	
	// 基于情感分析的置信度
	sentimentConfidence := math.Abs(result.SentimentScore - 0.5) * 2
	confidence += sentimentConfidence * 0.3
	
	// 基于词向量质量的置信度
	if len(result.Embeddings) > 0 {
		confidence += 0.3
	}
	
	return math.Min(confidence, 1.0)
}

// extractFeatures 提取特征
func (smp *SimpleMLProcessor) extractFeatures(text string, embeddings [][]float64) map[string]float64 {
	features := make(map[string]float64)
	
	// 文本长度特征
	features["text_length"] = float64(len(text))
	features["token_count"] = float64(len(embeddings))
	
	// 词向量统计特征
	if len(embeddings) > 0 {
		avgEmbedding := smp.calculateAverageEmbedding(embeddings)
		features["avg_embedding_norm"] = smp.calculateNorm(avgEmbedding)
	}
	
	// 语言特征
	features["chinese_ratio"] = smp.calculateChineseRatio(text)
	features["digit_ratio"] = smp.calculateDigitRatio(text)
	
	// 词汇特征
	knownWordCount := 0
	for _, embedding := range embeddings {
		if len(embedding) > 0 && embedding[0] > 0.05 { // 简单的已知词汇判断
			knownWordCount++
		}
	}
	if len(embeddings) > 0 {
		features["known_word_ratio"] = float64(knownWordCount) / float64(len(embeddings))
	}
	
	return features
}

// 辅助方法
func (smp *SimpleMLProcessor) basicProcess(text string) *MLResult {
	return &MLResult{
		Embeddings:      [][]float64{},
		Classifications: []ClassificationInfo{{Label: "综合", Score: 0.5, Confidence: 0.3}},
		SentimentScore:  0.5,
		SentimentLabel:  "中性",
		Confidence:      0.3,
		Features:        make(map[string]float64),
	}
}

func (smp *SimpleMLProcessor) calculateAverageEmbedding(embeddings [][]float64) []float64 {
	if len(embeddings) == 0 {
		return []float64{}
	}
	
	dim := len(embeddings[0])
	avg := make([]float64, dim)
	
	for _, embedding := range embeddings {
		for i, val := range embedding {
			if i < dim {
				avg[i] += val
			}
		}
	}
	
	for i := range avg {
		avg[i] /= float64(len(embeddings))
	}
	
	return avg
}

func (smp *SimpleMLProcessor) calculateCosineSimilarity(a, b []float64) float64 {
	if len(a) != len(b) || len(a) == 0 {
		return 0.0
	}
	
	dotProduct := 0.0
	normA := 0.0
	normB := 0.0
	
	for i := 0; i < len(a); i++ {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}
	
	if normA == 0 || normB == 0 {
		return 0.0
	}
	
	return dotProduct / (math.Sqrt(normA) * math.Sqrt(normB))
}

func (smp *SimpleMLProcessor) calculateNorm(vector []float64) float64 {
	sum := 0.0
	for _, val := range vector {
		sum += val * val
	}
	return math.Sqrt(sum)
}

func (smp *SimpleMLProcessor) calculateChineseRatio(text string) float64 {
	if len(text) == 0 {
		return 0.0
	}
	
	chineseCount := 0
	totalCount := 0
	
	for _, r := range text {
		totalCount++
		if r >= 0x4e00 && r <= 0x9fff {
			chineseCount++
		}
	}
	
	return float64(chineseCount) / float64(totalCount)
}

func (smp *SimpleMLProcessor) calculateDigitRatio(text string) float64 {
	if len(text) == 0 {
		return 0.0
	}
	
	digitCount := 0
	for _, r := range text {
		if r >= '0' && r <= '9' {
			digitCount++
		}
	}
	
	return float64(digitCount) / float64(len(text))
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
