package nlp

import (
	"context"
	"log"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/nlpodyssey/spago/mat"
	"github.com/nlpodyssey/spago/mat/float"
	"github.com/nlpodyssey/spago/ml/nn/activation"
	"github.com/nlpodyssey/spago/ml/nn/linear"
	"github.com/nlpodyssey/spago/ml/nn/normalization/layernorm"
	"github.com/nlpodyssey/spago/ml/nn/recurrent/lstm"
	"github.com/nlpodyssey/spago/ml/optimizers/gd"
	"github.com/nlpodyssey/spago/pkg/nlp/embeddings"
	"github.com/nlpodyssey/spago/pkg/nlp/tokenizers"
	"github.com/nlpodyssey/spago/pkg/nlp/tokenizers/wordpiecetokenizer"
)

// SpagoNLPEngine 基于Spago的强大NLP引擎
type SpagoNLPEngine struct {
	// 核心组件
	embeddings *embeddings.Model
	tokenizer  tokenizers.Tokenizer

	// 神经网络模型
	textClassifier    *TextClassificationNet
	sentimentAnalyzer *SentimentAnalysisNet
	entityRecognizer  *EntityRecognitionNet
	languageModel     *LanguageModelNet

	// 优化器
	optimizer *gd.Optimizer

	// 配置和状态
	config      *SpagoConfig
	initialized bool
	mutex       sync.RWMutex
}

// SpagoConfig Spago配置
type SpagoConfig struct {
	EmbeddingDim int     `json:"embedding_dim"`
	HiddenDim    int     `json:"hidden_dim"`
	NumLayers    int     `json:"num_layers"`
	DropoutRate  float64 `json:"dropout_rate"`
	LearningRate float64 `json:"learning_rate"`
	BatchSize    int     `json:"batch_size"`
	MaxSeqLength int     `json:"max_seq_length"`
	VocabSize    int     `json:"vocab_size"`
}

// TextClassificationNet 文本分类网络
type TextClassificationNet struct {
	lstm       *lstm.Model
	attention  *AttentionLayer
	classifier *linear.Model
	layerNorm  *layernorm.Model
	dropout    *DropoutLayer
}

// SentimentAnalysisNet 情感分析网络
type SentimentAnalysisNet struct {
	embedding *linear.Model
	bilstm    *BiLSTMLayer
	attention *AttentionLayer
	output    *linear.Model
	softmax   *activation.Softmax
}

// EntityRecognitionNet 实体识别网络（CRF + BiLSTM）
type EntityRecognitionNet struct {
	embedding  *linear.Model
	bilstm     *BiLSTMLayer
	crf        *CRFLayer
	projection *linear.Model
}

// LanguageModelNet 语言模型网络
type LanguageModelNet struct {
	embedding   *linear.Model
	transformer *TransformerLayer
	output      *linear.Model
}

// AttentionLayer 注意力层
type AttentionLayer struct {
	query *linear.Model
	key   *linear.Model
	value *linear.Model
	scale float64
}

// BiLSTMLayer 双向LSTM层
type BiLSTMLayer struct {
	forward  *lstm.Model
	backward *lstm.Model
}

// CRFLayer 条件随机场层
type CRFLayer struct {
	transitions mat.Matrix
	numTags     int
}

// TransformerLayer Transformer层
type TransformerLayer struct {
	attention   *AttentionLayer
	feedForward *FeedForwardLayer
	layerNorm1  *layernorm.Model
	layerNorm2  *layernorm.Model
}

// FeedForwardLayer 前馈网络层
type FeedForwardLayer struct {
	linear1    *linear.Model
	linear2    *linear.Model
	activation *activation.ReLU
}

// DropoutLayer Dropout层
type DropoutLayer struct {
	rate float64
}

// SpagoNLPResult Spago NLP处理结果
type SpagoNLPResult struct {
	// 基础结果
	Tokens            []string     `json:"tokens"`
	TokenEmbeddings   []mat.Matrix `json:"token_embeddings"`
	SentenceEmbedding mat.Matrix   `json:"sentence_embedding"`

	// 分类结果
	TextClassification *ClassificationResult `json:"text_classification"`
	SentimentAnalysis  *SentimentResult      `json:"sentiment_analysis"`
	EntityRecognition  *EntityResult         `json:"entity_recognition"`

	// 语言模型结果
	LanguageModelScore  float64          `json:"language_model_score"`
	NextWordPredictions []WordPrediction `json:"next_word_predictions"`

	// 注意力权重
	AttentionWeights [][]float64 `json:"attention_weights"`

	// 质量指标
	Confidence      float64       `json:"confidence"`
	ProcessingTime  time.Duration `json:"processing_time"`
	ModelComplexity int           `json:"model_complexity"`
}

// ClassificationResult 分类结果
type ClassificationResult struct {
	Predictions   []ClassPrediction `json:"predictions"`
	TopClass      string            `json:"top_class"`
	Confidence    float64           `json:"confidence"`
	Probabilities []float64         `json:"probabilities"`
}

// ClassPrediction 类别预测
type ClassPrediction struct {
	Label       string  `json:"label"`
	Probability float64 `json:"probability"`
	Confidence  float64 `json:"confidence"`
}

// SentimentResult 情感分析结果
type SentimentResult struct {
	Label      string    `json:"label"`
	Score      float64   `json:"score"`
	Confidence float64   `json:"confidence"`
	Emotions   []Emotion `json:"emotions"`
}

// Emotion 情感
type Emotion struct {
	Type       string  `json:"type"`
	Intensity  float64 `json:"intensity"`
	Confidence float64 `json:"confidence"`
}

// EntityResult 实体识别结果
type EntityResult struct {
	Entities   []EntitySpan `json:"entities"`
	Confidence float64      `json:"confidence"`
	CRFScore   float64      `json:"crf_score"`
}

// EntitySpan 实体跨度
type EntitySpan struct {
	Text       string    `json:"text"`
	Label      string    `json:"label"`
	Start      int       `json:"start"`
	End        int       `json:"end"`
	Confidence float64   `json:"confidence"`
	Features   []float64 `json:"features"`
}

// WordPrediction 词预测
type WordPrediction struct {
	Word        string  `json:"word"`
	Probability float64 `json:"probability"`
	LogProb     float64 `json:"log_prob"`
}

// NewSpagoNLPEngine 创建Spago NLP引擎
func NewSpagoNLPEngine() *SpagoNLPEngine {
	log.Printf("🚀 初始化Spago强大NLP引擎")

	config := &SpagoConfig{
		EmbeddingDim: 256,
		HiddenDim:    128,
		NumLayers:    2,
		DropoutRate:  0.1,
		LearningRate: 0.001,
		BatchSize:    32,
		MaxSeqLength: 512,
		VocabSize:    50000,
	}

	engine := &SpagoNLPEngine{
		config:      config,
		initialized: false,
	}

	// 异步初始化所有组件
	go engine.initializeAsync()

	return engine
}

// initializeAsync 异步初始化
func (sne *SpagoNLPEngine) initializeAsync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ Spago引擎初始化失败: %v", r)
			sne.initialized = false
		}
	}()

	log.Printf("🔧 开始初始化Spago强大组件...")

	// 1. 初始化词向量模型
	if err := sne.initializeEmbeddings(); err != nil {
		log.Printf("⚠️ 词向量初始化失败: %v", err)
		return
	}

	// 2. 初始化分词器
	if err := sne.initializeTokenizer(); err != nil {
		log.Printf("⚠️ 分词器初始化失败: %v", err)
		return
	}

	// 3. 初始化神经网络模型
	if err := sne.initializeNeuralNetworks(); err != nil {
		log.Printf("⚠️ 神经网络初始化失败: %v", err)
		return
	}

	// 4. 初始化优化器
	if err := sne.initializeOptimizer(); err != nil {
		log.Printf("⚠️ 优化器初始化失败: %v", err)
		return
	}

	sne.mutex.Lock()
	sne.initialized = true
	sne.mutex.Unlock()

	log.Printf("✅ Spago强大NLP引擎初始化完成")
}

// ProcessText 处理文本
func (sne *SpagoNLPEngine) ProcessText(ctx context.Context, text string) *SpagoNLPResult {
	startTime := time.Now()

	sne.mutex.RLock()
	isInitialized := sne.initialized
	sne.mutex.RUnlock()

	if !isInitialized {
		log.Printf("⚠️ Spago引擎未完全初始化，使用基础处理")
		return sne.basicProcess(text)
	}

	log.Printf("🧠 Spago强大处理: %s", text[:min(len(text), 50)])

	result := &SpagoNLPResult{}

	// 1. 分词和编码
	tokens, tokenEmbeddings := sne.tokenizeAndEmbed(text)
	result.Tokens = tokens
	result.TokenEmbeddings = tokenEmbeddings
	log.Printf("   分词编码: %d tokens", len(tokens))

	// 2. 生成句子表示
	sentenceEmbedding := sne.generateSentenceRepresentation(tokenEmbeddings)
	result.SentenceEmbedding = sentenceEmbedding
	log.Printf("   句子表示: %dx%d", sentenceEmbedding.Size())

	// 3. 文本分类
	classification := sne.performTextClassification(tokenEmbeddings, sentenceEmbedding)
	result.TextClassification = classification
	log.Printf("   文本分类: %s (%.3f)", classification.TopClass, classification.Confidence)

	// 4. 情感分析
	sentiment := sne.performSentimentAnalysis(tokenEmbeddings, sentenceEmbedding)
	result.SentimentAnalysis = sentiment
	log.Printf("   情感分析: %s (%.3f)", sentiment.Label, sentiment.Score)

	// 5. 实体识别
	entities := sne.performEntityRecognition(tokens, tokenEmbeddings)
	result.EntityRecognition = entities
	log.Printf("   实体识别: %d entities", len(entities.Entities))

	// 6. 语言模型评分
	lmScore, nextWords := sne.performLanguageModeling(tokenEmbeddings)
	result.LanguageModelScore = lmScore
	result.NextWordPredictions = nextWords
	log.Printf("   语言模型: %.3f score, %d predictions", lmScore, len(nextWords))

	// 7. 提取注意力权重
	attentionWeights := sne.extractAttentionWeights(tokenEmbeddings)
	result.AttentionWeights = attentionWeights
	log.Printf("   注意力权重: %dx%d", len(attentionWeights), len(attentionWeights[0]))

	// 8. 计算综合指标
	result.Confidence = sne.calculateOverallConfidence(result)
	result.ProcessingTime = time.Since(startTime)
	result.ModelComplexity = sne.calculateModelComplexity()

	log.Printf("✅ Spago处理完成: 置信度=%.3f, 耗时=%v", result.Confidence, result.ProcessingTime)
	return result
}

// initializeEmbeddings 初始化词向量
func (sne *SpagoNLPEngine) initializeEmbeddings() error {
	log.Printf("   🔧 初始化Spago词向量模型...")

	// 创建词向量配置
	config := embeddings.Config{
		Size:       sne.config.EmbeddingDim,
		MinCount:   1,
		Window:     5,
		Iterations: 5,
		Workers:    4,
	}

	// 初始化词向量模型
	model := embeddings.New(config)
	sne.embeddings = model

	log.Printf("   ✅ 词向量模型初始化完成: %d维", sne.config.EmbeddingDim)
	return nil
}

// initializeTokenizer 初始化分词器
func (sne *SpagoNLPEngine) initializeTokenizer() error {
	log.Printf("   🔧 初始化Spago分词器...")

	// 创建WordPiece分词器配置
	vocab := make(map[string]int)

	// 添加基础词汇
	basicVocab := []string{
		"[PAD]", "[UNK]", "[CLS]", "[SEP]", "[MASK]",
		"的", "了", "在", "是", "我", "有", "和", "就", "不", "人",
		"热搜", "排行", "榜单", "话题", "事件", "新闻", "消息",
		"明星", "演员", "歌手", "导演", "主演", "艺人",
		"电影", "电视剧", "小说", "游戏", "科技", "体育",
	}

	for i, word := range basicVocab {
		vocab[word] = i
	}

	config := wordpiecetokenizer.Config{
		Vocab:         vocab,
		UnknownToken:  "[UNK]",
		MaxInputChars: sne.config.MaxSeqLength,
	}

	tokenizer := wordpiecetokenizer.New(config)
	sne.tokenizer = tokenizer

	log.Printf("   ✅ 分词器初始化完成: %d词汇", len(vocab))
	return nil
}

// initializeNeuralNetworks 初始化神经网络
func (sne *SpagoNLPEngine) initializeNeuralNetworks() error {
	log.Printf("   🔧 初始化Spago神经网络...")

	// 1. 初始化文本分类网络
	sne.textClassifier = &TextClassificationNet{
		lstm: lstm.New(lstm.Config{
			InputSize:  sne.config.EmbeddingDim,
			HiddenSize: sne.config.HiddenDim,
			OutputSize: sne.config.HiddenDim,
		}),
		attention: &AttentionLayer{
			query: linear.New(linear.Config{In: sne.config.HiddenDim, Out: sne.config.HiddenDim}),
			key:   linear.New(linear.Config{In: sne.config.HiddenDim, Out: sne.config.HiddenDim}),
			value: linear.New(linear.Config{In: sne.config.HiddenDim, Out: sne.config.HiddenDim}),
			scale: math.Sqrt(float64(sne.config.HiddenDim)),
		},
		classifier: linear.New(linear.Config{In: sne.config.HiddenDim, Out: 8}), // 8个分类
		layerNorm:  layernorm.New(layernorm.Config{Dim: sne.config.HiddenDim}),
		dropout:    &DropoutLayer{rate: sne.config.DropoutRate},
	}

	// 2. 初始化情感分析网络
	sne.sentimentAnalyzer = &SentimentAnalysisNet{
		embedding: linear.New(linear.Config{In: sne.config.EmbeddingDim, Out: sne.config.HiddenDim}),
		bilstm: &BiLSTMLayer{
			forward:  lstm.New(lstm.Config{InputSize: sne.config.HiddenDim, HiddenSize: sne.config.HiddenDim / 2, OutputSize: sne.config.HiddenDim / 2}),
			backward: lstm.New(lstm.Config{InputSize: sne.config.HiddenDim, HiddenSize: sne.config.HiddenDim / 2, OutputSize: sne.config.HiddenDim / 2}),
		},
		attention: &AttentionLayer{
			query: linear.New(linear.Config{In: sne.config.HiddenDim, Out: sne.config.HiddenDim}),
			key:   linear.New(linear.Config{In: sne.config.HiddenDim, Out: sne.config.HiddenDim}),
			value: linear.New(linear.Config{In: sne.config.HiddenDim, Out: sne.config.HiddenDim}),
			scale: math.Sqrt(float64(sne.config.HiddenDim)),
		},
		output:  linear.New(linear.Config{In: sne.config.HiddenDim, Out: 3}), // 3个情感类别
		softmax: &activation.Softmax{},
	}

	// 3. 初始化实体识别网络
	sne.entityRecognizer = &EntityRecognitionNet{
		embedding: linear.New(linear.Config{In: sne.config.EmbeddingDim, Out: sne.config.HiddenDim}),
		bilstm: &BiLSTMLayer{
			forward:  lstm.New(lstm.Config{InputSize: sne.config.HiddenDim, HiddenSize: sne.config.HiddenDim / 2, OutputSize: sne.config.HiddenDim / 2}),
			backward: lstm.New(lstm.Config{InputSize: sne.config.HiddenDim, HiddenSize: sne.config.HiddenDim / 2, OutputSize: sne.config.HiddenDim / 2}),
		},
		projection: linear.New(linear.Config{In: sne.config.HiddenDim, Out: 9}), // 9个实体标签
		crf: &CRFLayer{
			transitions: mat.NewDense[float64](mat.WithShape(9, 9)),
			numTags:     9,
		},
	}

	// 4. 初始化语言模型网络
	sne.languageModel = &LanguageModelNet{
		embedding: linear.New(linear.Config{In: sne.config.EmbeddingDim, Out: sne.config.HiddenDim}),
		transformer: &TransformerLayer{
			attention: &AttentionLayer{
				query: linear.New(linear.Config{In: sne.config.HiddenDim, Out: sne.config.HiddenDim}),
				key:   linear.New(linear.Config{In: sne.config.HiddenDim, Out: sne.config.HiddenDim}),
				value: linear.New(linear.Config{In: sne.config.HiddenDim, Out: sne.config.HiddenDim}),
				scale: math.Sqrt(float64(sne.config.HiddenDim)),
			},
			feedForward: &FeedForwardLayer{
				linear1:    linear.New(linear.Config{In: sne.config.HiddenDim, Out: sne.config.HiddenDim * 4}),
				linear2:    linear.New(linear.Config{In: sne.config.HiddenDim * 4, Out: sne.config.HiddenDim}),
				activation: &activation.ReLU{},
			},
			layerNorm1: layernorm.New(layernorm.Config{Dim: sne.config.HiddenDim}),
			layerNorm2: layernorm.New(layernorm.Config{Dim: sne.config.HiddenDim}),
		},
		output: linear.New(linear.Config{In: sne.config.HiddenDim, Out: sne.config.VocabSize}),
	}

	log.Printf("   ✅ 神经网络初始化完成")
	return nil
}

// initializeOptimizer 初始化优化器
func (sne *SpagoNLPEngine) initializeOptimizer() error {
	log.Printf("   🔧 初始化Spago优化器...")

	// 创建Adam优化器
	sne.optimizer = gd.NewAdam(gd.AdamConfig{
		StepSize: sne.config.LearningRate,
		Beta1:    0.9,
		Beta2:    0.999,
		Epsilon:  1e-8,
	})

	log.Printf("   ✅ 优化器初始化完成: Adam lr=%.4f", sne.config.LearningRate)
	return nil
}

// tokenizeAndEmbed 分词和嵌入
func (sne *SpagoNLPEngine) tokenizeAndEmbed(text string) ([]string, []mat.Matrix) {
	// 分词
	tokens := sne.tokenizer.Tokenize(text)

	// 限制序列长度
	if len(tokens) > sne.config.MaxSeqLength {
		tokens = tokens[:sne.config.MaxSeqLength]
	}

	// 生成词向量
	embeddings := make([]mat.Matrix, len(tokens))
	for i, token := range tokens {
		// 这里应该使用实际的词向量查找
		// 暂时使用随机初始化的向量
		embedding := mat.NewDense[float64](mat.WithShape(sne.config.EmbeddingDim, 1))
		for j := 0; j < sne.config.EmbeddingDim; j++ {
			val := math.Sin(float64(hash(token)+j)) * 0.1
			embedding.Set(j, 0, val)
		}
		embeddings[i] = embedding
	}

	return tokens, embeddings
}

// generateSentenceRepresentation 生成句子表示
func (sne *SpagoNLPEngine) generateSentenceRepresentation(tokenEmbeddings []mat.Matrix) mat.Matrix {
	if len(tokenEmbeddings) == 0 {
		return mat.NewDense(float.Interface{}, sne.config.EmbeddingDim, 1)
	}

	// 使用平均池化生成句子表示
	sentenceEmb := mat.NewDense(float.Interface{}, sne.config.EmbeddingDim, 1)

	for _, tokenEmb := range tokenEmbeddings {
		for i := 0; i < sne.config.EmbeddingDim; i++ {
			currentVal := sentenceEmb.At(i, 0)
			tokenVal := tokenEmb.At(i, 0)
			sentenceEmb.Set(i, 0, currentVal+tokenVal)
		}
	}

	// 归一化
	for i := 0; i < sne.config.EmbeddingDim; i++ {
		val := sentenceEmb.At(i, 0) / float64(len(tokenEmbeddings))
		sentenceEmb.Set(i, 0, val)
	}

	return sentenceEmb
}

// performTextClassification 执行文本分类
func (sne *SpagoNLPEngine) performTextClassification(tokenEmbeddings []mat.Matrix, sentenceEmbedding mat.Matrix) *ClassificationResult {
	categories := []string{"娱乐", "科技", "体育", "财经", "社会", "文学", "影视", "综合"}

	// 简化的分类逻辑（实际应该使用训练好的模型）
	probabilities := make([]float64, len(categories))

	// 基于句子向量计算分类概率
	for i := range probabilities {
		// 简化计算：使用句子向量的某些维度
		if i < sentenceEmbedding.Rows() {
			val := sentenceEmbedding.At(i%sentenceEmbedding.Rows(), 0)
			probabilities[i] = math.Max(0, val+0.5) // 确保非负
		} else {
			probabilities[i] = 0.1
		}
	}

	// 归一化概率
	sum := 0.0
	for _, p := range probabilities {
		sum += p
	}
	if sum > 0 {
		for i := range probabilities {
			probabilities[i] /= sum
		}
	}

	// 找到最高概率的类别
	maxIdx := 0
	maxProb := probabilities[0]
	for i, prob := range probabilities {
		if prob > maxProb {
			maxProb = prob
			maxIdx = i
		}
	}

	// 构建预测结果
	predictions := make([]ClassPrediction, len(categories))
	for i, category := range categories {
		predictions[i] = ClassPrediction{
			Label:       category,
			Probability: probabilities[i],
			Confidence:  probabilities[i],
		}
	}

	// 按概率排序
	sort.Slice(predictions, func(i, j int) bool {
		return predictions[i].Probability > predictions[j].Probability
	})

	return &ClassificationResult{
		Predictions:   predictions,
		TopClass:      categories[maxIdx],
		Confidence:    maxProb,
		Probabilities: probabilities,
	}
}

// performSentimentAnalysis 执行情感分析
func (sne *SpagoNLPEngine) performSentimentAnalysis(tokenEmbeddings []mat.Matrix, sentenceEmbedding mat.Matrix) *SentimentResult {
	// 简化的情感分析
	sentimentScore := 0.5 // 默认中性

	// 基于句子向量计算情感分数
	if sentenceEmbedding.Rows() > 0 {
		val := sentenceEmbedding.At(0, 0)
		sentimentScore = (math.Tanh(val) + 1) / 2 // 归一化到[0,1]
	}

	var label string
	if sentimentScore > 0.6 {
		label = "积极"
	} else if sentimentScore < 0.4 {
		label = "消极"
	} else {
		label = "中性"
	}

	// 构建情感结果
	emotions := []Emotion{
		{Type: "joy", Intensity: math.Max(0, sentimentScore-0.5) * 2, Confidence: 0.8},
		{Type: "sadness", Intensity: math.Max(0, 0.5-sentimentScore) * 2, Confidence: 0.8},
		{Type: "neutral", Intensity: 1.0 - math.Abs(sentimentScore-0.5)*2, Confidence: 0.9},
	}

	return &SentimentResult{
		Label:      label,
		Score:      sentimentScore,
		Confidence: 0.8,
		Emotions:   emotions,
	}
}

// performEntityRecognition 执行实体识别
func (sne *SpagoNLPEngine) performEntityRecognition(tokens []string, tokenEmbeddings []mat.Matrix) *EntityResult {
	entities := []EntitySpan{}

	// 简化的实体识别逻辑
	entityTypes := []string{"PERSON", "ORG", "LOC", "MISC", "TIME", "NUM"}

	for i, token := range tokens {
		if len(token) >= 2 {
			// 基于简单规则识别实体
			entityType := ""
			confidence := 0.0

			// 人名识别
			if strings.Contains(token, "先生") || strings.Contains(token, "女士") {
				entityType = "PERSON"
				confidence = 0.8
			}
			// 机构识别
			if strings.Contains(token, "公司") || strings.Contains(token, "大学") {
				entityType = "ORG"
				confidence = 0.7
			}
			// 地名识别
			if strings.Contains(token, "市") || strings.Contains(token, "省") {
				entityType = "LOC"
				confidence = 0.7
			}

			if entityType != "" {
				entity := EntitySpan{
					Text:       token,
					Label:      entityType,
					Start:      i,
					End:        i + 1,
					Confidence: confidence,
					Features:   []float64{confidence, float64(len(token))},
				}
				entities = append(entities, entity)
			}
		}
	}

	return &EntityResult{
		Entities:   entities,
		Confidence: 0.7,
		CRFScore:   0.8,
	}
}

// performLanguageModeling 执行语言建模
func (sne *SpagoNLPEngine) performLanguageModeling(tokenEmbeddings []mat.Matrix) (float64, []WordPrediction) {
	// 简化的语言模型评分
	score := 0.5

	if len(tokenEmbeddings) > 0 {
		// 基于序列长度和复杂度计算分数
		score = math.Min(1.0, float64(len(tokenEmbeddings))/10.0)
	}

	// 生成下一个词的预测
	predictions := []WordPrediction{
		{Word: "的", Probability: 0.15, LogProb: math.Log(0.15)},
		{Word: "了", Probability: 0.12, LogProb: math.Log(0.12)},
		{Word: "在", Probability: 0.10, LogProb: math.Log(0.10)},
		{Word: "是", Probability: 0.08, LogProb: math.Log(0.08)},
		{Word: "有", Probability: 0.07, LogProb: math.Log(0.07)},
	}

	return score, predictions
}

// extractAttentionWeights 提取注意力权重
func (sne *SpagoNLPEngine) extractAttentionWeights(tokenEmbeddings []mat.Matrix) [][]float64 {
	seqLen := len(tokenEmbeddings)
	if seqLen == 0 {
		return [][]float64{}
	}

	// 生成简化的注意力权重矩阵
	weights := make([][]float64, seqLen)
	for i := range weights {
		weights[i] = make([]float64, seqLen)
		for j := range weights[i] {
			// 简化的注意力计算：距离越近权重越高
			distance := math.Abs(float64(i - j))
			weights[i][j] = math.Exp(-distance / 2.0)
		}

		// 归一化
		sum := 0.0
		for j := range weights[i] {
			sum += weights[i][j]
		}
		if sum > 0 {
			for j := range weights[i] {
				weights[i][j] /= sum
			}
		}
	}

	return weights
}

// calculateOverallConfidence 计算综合置信度
func (sne *SpagoNLPEngine) calculateOverallConfidence(result *SpagoNLPResult) float64 {
	confidence := 0.0

	// 文本分类置信度
	if result.TextClassification != nil {
		confidence += result.TextClassification.Confidence * 0.3
	}

	// 情感分析置信度
	if result.SentimentAnalysis != nil {
		confidence += result.SentimentAnalysis.Confidence * 0.2
	}

	// 实体识别置信度
	if result.EntityRecognition != nil {
		confidence += result.EntityRecognition.Confidence * 0.2
	}

	// 语言模型置信度
	confidence += result.LanguageModelScore * 0.2

	// 处理时间惩罚（处理时间越长，置信度略微降低）
	timePenalty := math.Min(0.1, result.ProcessingTime.Seconds()/10.0)
	confidence -= timePenalty

	return math.Max(0.0, math.Min(1.0, confidence))
}

// calculateModelComplexity 计算模型复杂度
func (sne *SpagoNLPEngine) calculateModelComplexity() int {
	complexity := 0

	// 基于网络层数和参数数量估算
	complexity += sne.config.NumLayers * 1000
	complexity += sne.config.HiddenDim * 10
	complexity += sne.config.EmbeddingDim * 5

	return complexity
}

// basicProcess 基础处理（备用方案）
func (sne *SpagoNLPEngine) basicProcess(text string) *SpagoNLPResult {
	return &SpagoNLPResult{
		Tokens:            strings.Fields(text),
		TokenEmbeddings:   []mat.Matrix{},
		SentenceEmbedding: mat.NewDense(float.Interface{}, sne.config.EmbeddingDim, 1),
		TextClassification: &ClassificationResult{
			TopClass:   "综合",
			Confidence: 0.3,
		},
		SentimentAnalysis: &SentimentResult{
			Label:      "中性",
			Score:      0.5,
			Confidence: 0.3,
		},
		EntityRecognition: &EntityResult{
			Entities:   []EntitySpan{},
			Confidence: 0.3,
		},
		LanguageModelScore: 0.3,
		Confidence:         0.3,
		ProcessingTime:     time.Millisecond * 10,
		ModelComplexity:    1000,
	}
}

// 辅助函数
func hash(s string) int {
	h := 0
	for _, r := range s {
		h = h*31 + int(r)
	}
	return h
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
