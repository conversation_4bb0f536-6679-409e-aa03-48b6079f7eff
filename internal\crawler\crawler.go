package crawler

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"faq-system/internal/learning"
)

// CrawlerConfig 爬虫配置
type CrawlerConfig struct {
	MaxConcurrency   int           `json:"max_concurrency"`   // 最大并发数
	RequestDelay     time.Duration `json:"request_delay"`     // 请求间隔
	Timeout          time.Duration `json:"timeout"`           // 请求超时
	UserAgent        string        `json:"user_agent"`        // User-Agent
	MaxRetries       int           `json:"max_retries"`       // 最大重试次数
	EnableJavaScript bool          `json:"enable_javascript"` // 是否启用JavaScript渲染
}

// CrawlTarget 爬取目标
type CrawlTarget struct {
	ID          int                    `json:"id"`
	Name        string                 `json:"name"`
	URL         string                 `json:"url"`
	Type        string                 `json:"type"`      // website, api, rss, search_engine
	Category    string                 `json:"category"`  // 知识分类
	Keywords    []string               `json:"keywords"`  // 关键词
	Selectors   map[string]string      `json:"selectors"` // CSS选择器
	Filters     map[string]interface{} `json:"filters"`   // 过滤条件
	Schedule    string                 `json:"schedule"`  // 调度表达式 (cron)
	Enabled     bool                   `json:"enabled"`   // 是否启用
	LastCrawled time.Time              `json:"last_crawled"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// CrawlResult 爬取结果
type CrawlResult struct {
	TargetID    int                    `json:"target_id"`
	URL         string                 `json:"url"`
	Title       string                 `json:"title"`
	Content     string                 `json:"content"`
	Summary     string                 `json:"summary"`
	Keywords    []string               `json:"keywords"`
	Category    string                 `json:"category"`
	Metadata    map[string]interface{} `json:"metadata"`
	CrawledAt   time.Time              `json:"crawled_at"`
	ProcessedAt time.Time              `json:"processed_at"`
	Status      string                 `json:"status"` // pending, processed, failed
}

// KnowledgeCrawler 知识爬虫
type KnowledgeCrawler struct {
	db               *sql.DB
	config           *CrawlerConfig
	knowledgeLearner *learning.KnowledgeLearner
	httpClient       *http.Client
	targets          map[int]*CrawlTarget
	targetsMutex     sync.RWMutex
	activeCrawls     map[int]*CrawlTarget // 正在爬取的目标
	activeMutex      sync.RWMutex
	stopChan         chan struct{}
	wg               sync.WaitGroup
	running          bool
}

// NewKnowledgeCrawler 创建知识爬虫
func NewKnowledgeCrawler(db *sql.DB, knowledgeLearner *learning.KnowledgeLearner) *KnowledgeCrawler {
	config := &CrawlerConfig{
		MaxConcurrency:   5,
		RequestDelay:     time.Second * 2,
		Timeout:          time.Second * 30,
		UserAgent:        "FAQ-System-Crawler/1.0",
		MaxRetries:       3,
		EnableJavaScript: false,
	}

	httpClient := &http.Client{
		Timeout: config.Timeout,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	return &KnowledgeCrawler{
		db:               db,
		config:           config,
		knowledgeLearner: knowledgeLearner,
		httpClient:       httpClient,
		targets:          make(map[int]*CrawlTarget),
		activeCrawls:     make(map[int]*CrawlTarget),
		stopChan:         make(chan struct{}),
	}
}

// Start 启动爬虫
func (kc *KnowledgeCrawler) Start() error {
	if kc.running {
		return fmt.Errorf("爬虫已经在运行中")
	}

	log.Println("🕷️ 启动知识爬虫...")

	// 加载爬取目标
	if err := kc.loadTargets(); err != nil {
		return fmt.Errorf("加载爬取目标失败: %v", err)
	}

	kc.running = true

	// 启动调度器
	kc.wg.Add(1)
	go kc.scheduler()

	// 启动处理器
	kc.wg.Add(1)
	go kc.processor()

	log.Println("✅ 知识爬虫启动成功")
	return nil
}

// Stop 停止爬虫
func (kc *KnowledgeCrawler) Stop() {
	if !kc.running {
		return
	}

	log.Println("🛑 停止知识爬虫...")
	kc.running = false
	close(kc.stopChan)
	kc.wg.Wait()
	log.Println("✅ 知识爬虫已停止")
}

// AddTarget 添加爬取目标
func (kc *KnowledgeCrawler) AddTarget(target *CrawlTarget) error {
	// 验证目标配置
	if err := kc.validateTarget(target); err != nil {
		return fmt.Errorf("目标配置无效: %v", err)
	}

	// 保存到数据库
	query := `
		INSERT INTO crawl_targets 
		(name, url, type, category, keywords, selectors, filters, schedule, enabled, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`

	keywordsJSON, _ := json.Marshal(target.Keywords)
	selectorsJSON, _ := json.Marshal(target.Selectors)
	filtersJSON, _ := json.Marshal(target.Filters)

	result, err := kc.db.Exec(query, target.Name, target.URL, target.Type, target.Category,
		keywordsJSON, selectorsJSON, filtersJSON, target.Schedule, target.Enabled)
	if err != nil {
		return fmt.Errorf("保存爬取目标失败: %v", err)
	}

	id, _ := result.LastInsertId()
	target.ID = int(id)
	target.CreatedAt = time.Now()
	target.UpdatedAt = time.Now()

	// 添加到内存缓存
	kc.targetsMutex.Lock()
	kc.targets[target.ID] = target
	kc.targetsMutex.Unlock()

	log.Printf("✅ 添加爬取目标: %s (%s)", target.Name, target.URL)
	return nil
}

// RemoveTarget 移除爬取目标
func (kc *KnowledgeCrawler) RemoveTarget(targetID int) error {
	// 从数据库删除
	_, err := kc.db.Exec("DELETE FROM crawl_targets WHERE id = ?", targetID)
	if err != nil {
		return fmt.Errorf("删除爬取目标失败: %v", err)
	}

	// 从内存缓存删除
	kc.targetsMutex.Lock()
	delete(kc.targets, targetID)
	kc.targetsMutex.Unlock()

	log.Printf("✅ 删除爬取目标: %d", targetID)
	return nil
}

// GetTargets 获取所有爬取目标
func (kc *KnowledgeCrawler) GetTargets() []*CrawlTarget {
	kc.targetsMutex.RLock()
	defer kc.targetsMutex.RUnlock()

	targets := make([]*CrawlTarget, 0, len(kc.targets))
	for _, target := range kc.targets {
		targets = append(targets, target)
	}
	return targets
}

// GetActiveCrawls 获取正在爬取的目标
func (kc *KnowledgeCrawler) GetActiveCrawls() []*CrawlTarget {
	kc.activeMutex.RLock()
	defer kc.activeMutex.RUnlock()

	active := make([]*CrawlTarget, 0, len(kc.activeCrawls))
	for _, target := range kc.activeCrawls {
		active = append(active, target)
	}
	return active
}

// IsRunning 获取爬虫运行状态
func (kc *KnowledgeCrawler) IsRunning() bool {
	return kc.running
}

// UpdateTarget 更新爬取目标
func (kc *KnowledgeCrawler) UpdateTarget(target *CrawlTarget) error {
	// 验证目标配置
	if err := kc.validateTarget(target); err != nil {
		return fmt.Errorf("目标配置无效: %v", err)
	}

	// 更新数据库
	query := `
		UPDATE crawl_targets
		SET name = ?, url = ?, type = ?, category = ?, keywords = ?,
		    selectors = ?, filters = ?, schedule = ?, enabled = ?, updated_at = NOW()
		WHERE id = ?
	`

	keywordsJSON, _ := json.Marshal(target.Keywords)
	selectorsJSON, _ := json.Marshal(target.Selectors)
	filtersJSON, _ := json.Marshal(target.Filters)

	_, err := kc.db.Exec(query, target.Name, target.URL, target.Type, target.Category,
		keywordsJSON, selectorsJSON, filtersJSON, target.Schedule, target.Enabled, target.ID)
	if err != nil {
		return fmt.Errorf("更新爬取目标失败: %v", err)
	}

	// 更新内存缓存
	kc.targetsMutex.Lock()
	kc.targets[target.ID] = target
	kc.targetsMutex.Unlock()

	log.Printf("✅ 更新爬取目标: %s (%s)", target.Name, target.URL)
	return nil
}

// loadTargets 从数据库加载爬取目标
func (kc *KnowledgeCrawler) loadTargets() error {
	query := `
		SELECT id, name, url, type, category, keywords, selectors, filters, 
		       schedule, enabled, last_crawled, created_at, updated_at
		FROM crawl_targets
		WHERE enabled = 1
	`

	rows, err := kc.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	kc.targetsMutex.Lock()
	defer kc.targetsMutex.Unlock()

	for rows.Next() {
		var target CrawlTarget
		var keywordsJSON, selectorsJSON, filtersJSON string
		var lastCrawled sql.NullTime

		err := rows.Scan(&target.ID, &target.Name, &target.URL, &target.Type, &target.Category,
			&keywordsJSON, &selectorsJSON, &filtersJSON, &target.Schedule, &target.Enabled,
			&lastCrawled, &target.CreatedAt, &target.UpdatedAt)
		if err != nil {
			continue
		}

		json.Unmarshal([]byte(keywordsJSON), &target.Keywords)
		json.Unmarshal([]byte(selectorsJSON), &target.Selectors)
		json.Unmarshal([]byte(filtersJSON), &target.Filters)

		if lastCrawled.Valid {
			target.LastCrawled = lastCrawled.Time
		}

		kc.targets[target.ID] = &target
	}

	log.Printf("📊 加载了 %d 个爬取目标", len(kc.targets))
	return nil
}

// validateTarget 验证爬取目标配置
func (kc *KnowledgeCrawler) validateTarget(target *CrawlTarget) error {
	if target.Name == "" {
		return fmt.Errorf("目标名称不能为空")
	}
	if target.URL == "" {
		return fmt.Errorf("目标URL不能为空")
	}
	if target.Type == "" {
		target.Type = "website"
	}
	if target.Category == "" {
		target.Category = "general"
	}
	if target.Schedule == "" {
		target.Schedule = "0 */6 * * *" // 默认每6小时执行一次
	}
	return nil
}

// scheduler 调度器
func (kc *KnowledgeCrawler) scheduler() {
	defer kc.wg.Done()

	ticker := time.NewTicker(time.Minute) // 每1分钟检查一次，支持每分钟调度
	defer ticker.Stop()

	for {
		select {
		case <-kc.stopChan:
			return
		case <-ticker.C:
			kc.checkAndScheduleTasks()
		}
	}
}

// processor 处理器
func (kc *KnowledgeCrawler) processor() {
	defer kc.wg.Done()

	ticker := time.NewTicker(time.Second * 10) // 每10秒处理一次
	defer ticker.Stop()

	for {
		select {
		case <-kc.stopChan:
			return
		case <-ticker.C:
			kc.processResults()
		}
	}
}

// checkAndScheduleTasks 检查并调度任务
func (kc *KnowledgeCrawler) checkAndScheduleTasks() {
	kc.targetsMutex.RLock()
	targets := make([]*CrawlTarget, 0, len(kc.targets))
	for _, target := range kc.targets {
		if target.Enabled {
			targets = append(targets, target)
		}
	}
	kc.targetsMutex.RUnlock()

	for _, target := range targets {
		if kc.shouldCrawl(target) {
			kc.scheduleCrawlTask(target)
		}
	}
}

// shouldCrawl 判断是否应该爬取
func (kc *KnowledgeCrawler) shouldCrawl(target *CrawlTarget) bool {
	// 简单的时间间隔检查（实际应该使用cron表达式解析）
	if target.LastCrawled.IsZero() {
		return true
	}

	// 根据调度表达式判断
	switch target.Schedule {
	case "* * * * *": // 每分钟
		return time.Since(target.LastCrawled) >= time.Minute
	case "0 */1 * * *": // 每小时
		return time.Since(target.LastCrawled) >= time.Hour
	case "0 */6 * * *": // 每6小时
		return time.Since(target.LastCrawled) >= time.Hour*6
	case "0 0 * * *": // 每天
		return time.Since(target.LastCrawled) >= time.Hour*24
	case "0 0 * * 0": // 每周
		return time.Since(target.LastCrawled) >= time.Hour*24*7
	default:
		return time.Since(target.LastCrawled) >= time.Hour*6
	}
}

// scheduleCrawlTask 调度爬取任务
func (kc *KnowledgeCrawler) scheduleCrawlTask(target *CrawlTarget) {
	go func() {
		// 添加到活跃爬取列表
		kc.activeMutex.Lock()
		kc.activeCrawls[target.ID] = target
		kc.activeMutex.Unlock()

		log.Printf("🕷️ 开始爬取: %s (%s)", target.Name, target.URL)

		result, err := kc.crawlTarget(target)
		if err != nil {
			log.Printf("爬取失败 %s: %v", target.Name, err)
			// 从活跃列表中移除
			kc.activeMutex.Lock()
			delete(kc.activeCrawls, target.ID)
			kc.activeMutex.Unlock()
			return
		}

		// 保存爬取结果
		if err := kc.saveCrawlResult(result); err != nil {
			log.Printf("保存爬取结果失败: %v", err)
			// 从活跃列表中移除
			kc.activeMutex.Lock()
			delete(kc.activeCrawls, target.ID)
			kc.activeMutex.Unlock()
			return
		}

		// 更新最后爬取时间
		kc.updateLastCrawled(target.ID) //internal\crawler\crawler.go

		// 从活跃列表中移除
		kc.activeMutex.Lock()
		delete(kc.activeCrawls, target.ID)
		kc.activeMutex.Unlock()

		log.Printf("✅ 爬取完成: %s", target.Name)
	}()
}

// ManualCrawl 手动触发爬取
func (kc *KnowledgeCrawler) ManualCrawl(targetID int) error {
	kc.targetsMutex.RLock()
	target, exists := kc.targets[targetID]
	kc.targetsMutex.RUnlock()

	if !exists {
		return fmt.Errorf("目标不存在: %d", targetID)
	}

	// 检查是否已经在爬取中
	kc.activeMutex.RLock()
	_, isActive := kc.activeCrawls[targetID]
	kc.activeMutex.RUnlock()

	if isActive {
		return fmt.Errorf("目标正在爬取中: %s", target.Name)
	}

	// 立即启动爬取任务（不等待调度）
	log.Printf("🚀 手动触发爬取: %s (%s)", target.Name, target.URL)
	kc.scheduleCrawlTask(target)
	return nil
}
