package nlp

import (
	"log"
	"sort"
	"strings"
)

// AdvancedNLPProcessor 高级NLP处理器（集成gojieba和简化ML）
type AdvancedNLPProcessor struct {
	jiebaProcessor *JiebaProcessor
	mlProcessor    *SimpleMLProcessor
	initialized    bool
}

// AdvancedResult 高级处理结果
type AdvancedResult struct {
	// 基础NLP结果
	Tokens   []string   `json:"tokens"`   // 分词结果
	Keywords []WordInfo `json:"keywords"` // 关键词
	Entities []Entity   `json:"entities"` // 命名实体
	Topics   []Topic    `json:"topics"`   // 主题话题

	// 高级ML结果
	SpagoResult     *SpagoResult         `json:"spago_result"`    // Spago处理结果
	Classifications []ClassificationInfo `json:"classifications"` // 分类结果
	SentimentScore  float64              `json:"sentiment_score"` // 情感分数
	SentimentLabel  string               `json:"sentiment_label"` // 情感标签

	// 综合结果
	OverallConfidence float64                `json:"overall_confidence"` // 综合置信度
	ProcessingMethod  string                 `json:"processing_method"`  // 处理方法
	Features          map[string]float64     `json:"features"`           // 特征向量
	Metadata          map[string]interface{} `json:"metadata"`           // 元数据
}

// NewAdvancedNLPProcessor 创建高级NLP处理器
func NewAdvancedNLPProcessor() *AdvancedNLPProcessor {
	log.Printf("🚀 初始化高级NLP处理器（gojieba + 简化ML）")

	processor := &AdvancedNLPProcessor{
		jiebaProcessor: NewJiebaProcessor(),
		mlProcessor:    NewSimpleMLProcessor(),
		initialized:    false,
	}

	// 异步完成初始化
	go processor.completeInitialization()

	return processor
}

// completeInitialization 完成初始化
func (anp *AdvancedNLPProcessor) completeInitialization() {
	// 等待Spago初始化完成
	for i := 0; i < 30; i++ { // 最多等待30秒
		if anp.spagoProcessor.initialized {
			break
		}
		log.Printf("⏳ 等待Spago初始化... (%d/30)", i+1)
		// time.Sleep(time.Second) // 注释掉避免导入time包
	}

	anp.initialized = true
	log.Printf("✅ 高级NLP处理器初始化完成")
}

// ProcessText 处理文本
func (anp *AdvancedNLPProcessor) ProcessText(text string) *AdvancedResult {
	log.Printf("🧠 高级NLP处理文本: %s", text[:min(len(text), 50)])

	result := &AdvancedResult{
		Features: make(map[string]float64),
		Metadata: make(map[string]interface{}),
	}

	// 1. 基础NLP处理（gojieba）
	anp.performBasicNLP(text, result)

	// 2. 高级ML处理（Spago）
	if anp.initialized && anp.spagoProcessor.initialized {
		anp.performAdvancedML(text, result)
		result.ProcessingMethod = "gojieba + spago"
	} else {
		result.ProcessingMethod = "gojieba only"
		log.Printf("⚠️ Spago未完全初始化，仅使用gojieba处理")
	}

	// 3. 融合结果
	anp.fuseResults(result)

	// 4. 计算综合置信度
	result.OverallConfidence = anp.calculateOverallConfidence(result)

	log.Printf("✅ 高级NLP处理完成，置信度: %.2f", result.OverallConfidence)
	return result
}

// performBasicNLP 执行基础NLP处理
func (anp *AdvancedNLPProcessor) performBasicNLP(text string, result *AdvancedResult) {
	log.Printf("   🔧 执行基础NLP处理（gojieba）")

	// 分词
	result.Tokens = anp.jiebaProcessor.SegmentText(text)
	log.Printf("     分词: %d 个token", len(result.Tokens))

	// 关键词提取
	result.Keywords = anp.jiebaProcessor.ExtractKeywordsWithPOS(text, 10)
	log.Printf("     关键词: %d 个", len(result.Keywords))

	// 实体识别
	result.Entities = anp.jiebaProcessor.ExtractEntities(text)
	log.Printf("     实体: %d 个", len(result.Entities))

	// 主题提取
	result.Topics = anp.jiebaProcessor.ExtractTopics(text, 10)
	log.Printf("     主题: %d 个", len(result.Topics))

	// 情感分析
	sentiment, confidence := anp.jiebaProcessor.AnalyzeSentiment(text)
	result.SentimentLabel = sentiment
	result.SentimentScore = confidence
	log.Printf("     情感: %s (%.2f)", sentiment, confidence)
}

// performAdvancedML 执行高级ML处理
func (anp *AdvancedNLPProcessor) performAdvancedML(text string, result *AdvancedResult) {
	log.Printf("   🚀 执行高级ML处理（Spago）")

	// Spago处理
	spagoResult := anp.spagoProcessor.ProcessText(text)
	result.SpagoResult = spagoResult

	// 提取分类结果
	result.Classifications = spagoResult.Classifications
	log.Printf("     ML分类: %d 个类别", len(result.Classifications))

	// 合并特征
	for key, value := range spagoResult.Features {
		result.Features["spago_"+key] = value
	}
	log.Printf("     ML特征: %d 个", len(spagoResult.Features))
}

// fuseResults 融合结果
func (anp *AdvancedNLPProcessor) fuseResults(result *AdvancedResult) {
	log.Printf("   🔀 融合处理结果")

	// 1. 融合情感分析结果
	anp.fuseSentimentResults(result)

	// 2. 融合分类结果
	anp.fuseClassificationResults(result)

	// 3. 增强特征向量
	anp.enhanceFeatures(result)

	// 4. 添加元数据
	anp.addMetadata(result)
}

// fuseSentimentResults 融合情感分析结果
func (anp *AdvancedNLPProcessor) fuseSentimentResults(result *AdvancedResult) {
	if result.SpagoResult == nil {
		return
	}

	// 加权平均情感分数
	jiebaWeight := 0.6
	spagoWeight := 0.4

	fusedScore := result.SentimentScore*jiebaWeight + result.SpagoResult.SentimentScore*spagoWeight
	result.SentimentScore = fusedScore

	// 重新确定情感标签
	if fusedScore > 0.6 {
		result.SentimentLabel = "积极"
	} else if fusedScore < 0.4 {
		result.SentimentLabel = "消极"
	} else {
		result.SentimentLabel = "中性"
	}

	log.Printf("     融合情感: %s (%.2f)", result.SentimentLabel, result.SentimentScore)
}

// fuseClassificationResults 融合分类结果
func (anp *AdvancedNLPProcessor) fuseClassificationResults(result *AdvancedResult) {
	if len(result.Classifications) == 0 {
		// 基于关键词和主题生成分类
		result.Classifications = anp.generateClassificationsFromKeywords(result.Keywords, result.Topics)
	}

	// 按置信度排序
	sort.Slice(result.Classifications, func(i, j int) bool {
		return result.Classifications[i].Confidence > result.Classifications[j].Confidence
	})

	log.Printf("     融合分类: %d 个类别", len(result.Classifications))
}

// enhanceFeatures 增强特征向量
func (anp *AdvancedNLPProcessor) enhanceFeatures(result *AdvancedResult) {
	// 添加基础统计特征
	result.Features["token_count"] = float64(len(result.Tokens))
	result.Features["keyword_count"] = float64(len(result.Keywords))
	result.Features["entity_count"] = float64(len(result.Entities))
	result.Features["topic_count"] = float64(len(result.Topics))

	// 添加质量特征
	if len(result.Keywords) > 0 {
		avgKeywordWeight := 0.0
		for _, kw := range result.Keywords {
			avgKeywordWeight += kw.Weight
		}
		result.Features["avg_keyword_weight"] = avgKeywordWeight / float64(len(result.Keywords))
	}

	// 添加多样性特征
	result.Features["entity_diversity"] = anp.calculateEntityDiversity(result.Entities)
	result.Features["topic_diversity"] = anp.calculateTopicDiversity(result.Topics)

	log.Printf("     增强特征: %d 个", len(result.Features))
}

// addMetadata 添加元数据
func (anp *AdvancedNLPProcessor) addMetadata(result *AdvancedResult) {
	result.Metadata["jieba_available"] = anp.jiebaProcessor != nil
	result.Metadata["spago_available"] = anp.spagoProcessor != nil && anp.spagoProcessor.initialized
	result.Metadata["processing_complete"] = anp.initialized

	// 添加处理统计
	if result.SpagoResult != nil {
		result.Metadata["spago_confidence"] = result.SpagoResult.Confidence
		result.Metadata["embedding_count"] = len(result.SpagoResult.Embeddings)
	}

	// 添加质量指标
	result.Metadata["has_entities"] = len(result.Entities) > 0
	result.Metadata["has_topics"] = len(result.Topics) > 0
	result.Metadata["has_keywords"] = len(result.Keywords) > 0
}

// calculateOverallConfidence 计算综合置信度
func (anp *AdvancedNLPProcessor) calculateOverallConfidence(result *AdvancedResult) float64 {
	confidence := 0.0

	// 基于基础NLP结果的置信度
	if len(result.Keywords) > 0 {
		confidence += 0.2
	}
	if len(result.Entities) > 0 {
		confidence += 0.2
	}
	if len(result.Topics) > 0 {
		confidence += 0.2
	}

	// 基于高级ML结果的置信度
	if result.SpagoResult != nil {
		confidence += result.SpagoResult.Confidence * 0.3
	}

	// 基于分类结果的置信度
	if len(result.Classifications) > 0 {
		confidence += result.Classifications[0].Confidence * 0.1
	}

	return min64(confidence, 1.0)
}

// generateClassificationsFromKeywords 基于关键词生成分类
func (anp *AdvancedNLPProcessor) generateClassificationsFromKeywords(keywords []WordInfo, topics []Topic) []ClassificationInfo {
	categoryScores := make(map[string]float64)

	// 基于关键词计算分类分数
	for _, kw := range keywords {
		category := anp.classifyKeyword(kw.Word)
		categoryScores[category] += kw.Weight
	}

	// 基于主题计算分类分数
	for _, topic := range topics {
		if topic.Category != "" {
			categoryScores[topic.Category] += topic.Confidence
		}
	}

	// 转换为分类信息
	classifications := []ClassificationInfo{}
	for category, score := range categoryScores {
		classifications = append(classifications, ClassificationInfo{
			Label:      category,
			Score:      score,
			Confidence: score * 0.7, // 简化的置信度计算
		})
	}

	return classifications
}

// classifyKeyword 关键词分类
func (anp *AdvancedNLPProcessor) classifyKeyword(keyword string) string {
	categories := map[string][]string{
		"娱乐": {"明星", "演员", "歌手", "综艺", "娱乐", "偶像"},
		"科技": {"科技", "技术", "AI", "互联网", "数码", "手机"},
		"体育": {"体育", "比赛", "运动", "球员", "赛事", "足球"},
		"财经": {"经济", "股票", "金融", "投资", "市场", "公司"},
		"社会": {"社会", "新闻", "事件", "政策", "民生", "国家"},
		"文学": {"小说", "作者", "作品", "文学", "书籍", "写作"},
		"影视": {"电影", "电视剧", "导演", "主演", "上映", "播出"},
	}

	for category, words := range categories {
		for _, word := range words {
			if strings.Contains(keyword, word) {
				return category
			}
		}
	}

	return "综合"
}

// calculateEntityDiversity 计算实体多样性
func (anp *AdvancedNLPProcessor) calculateEntityDiversity(entities []Entity) float64 {
	if len(entities) == 0 {
		return 0.0
	}

	typeCount := make(map[string]int)
	for _, entity := range entities {
		typeCount[entity.Type]++
	}

	return float64(len(typeCount)) / float64(len(entities))
}

// calculateTopicDiversity 计算主题多样性
func (anp *AdvancedNLPProcessor) calculateTopicDiversity(topics []Topic) float64 {
	if len(topics) == 0 {
		return 0.0
	}

	categoryCount := make(map[string]int)
	for _, topic := range topics {
		categoryCount[topic.Category]++
	}

	return float64(len(categoryCount)) / float64(len(topics))
}

// Close 关闭处理器
func (anp *AdvancedNLPProcessor) Close() {
	if anp.jiebaProcessor != nil {
		anp.jiebaProcessor.Close()
	}
	log.Printf("🔒 高级NLP处理器已关闭")
}

// 辅助函数
func min64(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}
