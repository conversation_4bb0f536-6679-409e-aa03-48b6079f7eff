package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 直接连接数据库
	db, err := sql.Open("mysql", "root:park%123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 检查爬取内容详情...")

	// 查询包含"百宝箱"的爬取结果
	query := `
		SELECT id, target_id, title, summary, content, status, crawled_at, processed_at
		FROM crawl_results 
		WHERE content LIKE '%百宝箱%'
		ORDER BY crawled_at DESC 
		LIMIT 3
	`
	
	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	count := 0
	for rows.Next() {
		var id, targetID int
		var title, summary, content, status, crawledAt string
		var processedAt sql.NullString

		err := rows.Scan(&id, &targetID, &title, &summary, &content, &status, &crawledAt, &processedAt)
		if err != nil {
			fmt.Printf("❌ 扫描失败: %v\n", err)
			continue
		}

		count++
		fmt.Printf("\n=== 记录 %d ===\n", count)
		fmt.Printf("ID: %d\n", id)
		fmt.Printf("目标ID: %d\n", targetID)
		fmt.Printf("标题: %s\n", title)
		fmt.Printf("摘要: %s\n", summary)
		fmt.Printf("状态: %s\n", status)
		fmt.Printf("爬取时间: %s\n", crawledAt)
		
		processedAtStr := "未处理"
		if processedAt.Valid {
			processedAtStr = processedAt.String
		}
		fmt.Printf("处理时间: %s\n", processedAtStr)
		
		fmt.Printf("内容长度: %d\n", len(content))
		
		// 显示内容的前500个字符
		if len(content) > 500 {
			fmt.Printf("内容预览: %s...\n", content[:500])
		} else {
			fmt.Printf("完整内容: %s\n", content)
		}
		
		fmt.Println("---")
	}

	if count == 0 {
		fmt.Println("❌ 没有找到包含'百宝箱'的记录")
	} else {
		fmt.Printf("✅ 找到 %d 个包含'百宝箱'的记录\n", count)
	}

	// 检查为什么没有生成学习知识
	fmt.Println("\n🔍 分析知识提取问题...")
	
	// 查询状态为processed但没有对应学习知识的记录
	analysisQuery := `
		SELECT cr.id, cr.title, cr.summary, LENGTH(cr.content) as content_len
		FROM crawl_results cr
		LEFT JOIN learned_knowledge lk ON lk.metadata LIKE CONCAT('%"target_id":', cr.target_id, '%')
		WHERE cr.status = 'processed' 
		AND cr.content LIKE '%百宝箱%'
		AND lk.id IS NULL
		LIMIT 5
	`
	
	analysisRows, err := db.Query(analysisQuery)
	if err != nil {
		fmt.Printf("❌ 分析查询失败: %v\n", err)
		return
	}
	defer analysisRows.Close()

	fmt.Println("已处理但没有生成学习知识的记录:")
	for analysisRows.Next() {
		var id, contentLen int
		var title, summary string
		
		analysisRows.Scan(&id, &title, &summary, &contentLen)
		
		fmt.Printf("  ID %d: 标题='%s', 摘要='%s', 内容长度=%d\n", id, title, summary, contentLen)
		
		// 分析为什么没有提取知识
		if title == "" && summary == "" {
			fmt.Println("    ❌ 问题: 标题和摘要都为空")
		} else if contentLen < 50 {
			fmt.Println("    ❌ 问题: 内容太短")
		} else {
			fmt.Println("    ❓ 问题: 其他原因")
		}
	}
}
