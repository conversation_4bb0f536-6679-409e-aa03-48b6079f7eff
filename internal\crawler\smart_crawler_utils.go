package crawler

import (
	"encoding/json"
	"math"
	"math/rand"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
)

// 访问状态管理
func (sc *SmartKnowledgeCrawler) isVisited(url string) bool {
	sc.visitedMutex.RLock()
	defer sc.visitedMutex.RUnlock()
	return sc.visitedURLs[url]
}

func (sc *SmartKnowledgeCrawler) markVisited(url string) {
	sc.visitedMutex.Lock()
	defer sc.visitedMutex.Unlock()
	sc.visitedURLs[url] = true
}

// 域名管理
func (sc *SmartKnowledgeCrawler) extractDomain(rawURL string) string {
	u, err := url.Parse(rawURL)
	if err != nil {
		return ""
	}
	return u.Host
}

func (sc *SmartKnowledgeCrawler) getDomainPageCount(domain string) int {
	sc.domainMutex.RLock()
	defer sc.domainMutex.RUnlock()
	return sc.domainPageCount[domain]
}

func (sc *SmartKnowledgeCrawler) incrementDomainPageCount(domain string) {
	sc.domainMutex.Lock()
	defer sc.domainMutex.Unlock()
	sc.domainPageCount[domain]++
}

// 域名检查
func (sc *SmartKnowledgeCrawler) isDomainAllowed(rawURL string, target *SmartCrawlTarget) bool {
	u, err := url.Parse(rawURL)
	if err != nil {
		return false
	}

	domain := u.Host

	// 检查禁止域名
	for _, blocked := range target.BlockedDomains {
		if strings.Contains(domain, blocked) {
			return false
		}
	}

	// 检查允许域名
	if len(target.AllowedDomains) > 0 {
		for _, allowed := range target.AllowedDomains {
			if strings.Contains(domain, allowed) {
				return true
			}
		}
		return false
	}

	return true
}

// 请求延迟
func (sc *SmartKnowledgeCrawler) applyRequestDelay(domain string) {
	sc.requestMutex.Lock()
	defer sc.requestMutex.Unlock()

	lastTime, exists := sc.lastRequestTime[domain]
	if exists {
		elapsed := time.Since(lastTime)
		delay := sc.config.RequestDelay

		// 随机延迟
		if sc.config.RandomDelay {
			randomFactor := 0.5 + rand.Float64() // 0.5-1.5倍
			delay = time.Duration(float64(delay) * randomFactor)
		}

		if elapsed < delay {
			time.Sleep(delay - elapsed)
		}
	}

	sc.lastRequestTime[domain] = time.Now()
}

// User-Agent轮换
func (sc *SmartKnowledgeCrawler) getRandomUserAgent() string {
	if len(sc.config.UserAgentRotation) == 0 {
		return "Mozilla/5.0 (compatible; SmartCrawler/1.0)"
	}

	sc.userAgentIndex = (sc.userAgentIndex + 1) % len(sc.config.UserAgentRotation)
	return sc.config.UserAgentRotation[sc.userAgentIndex]
}

// JavaScript渲染检测
func (sc *SmartKnowledgeCrawler) needsJSRendering(url string) bool {
	// 检测是否需要JavaScript渲染的简单规则
	jsPatterns := []string{
		"spa", "react", "vue", "angular", "ajax",
		"dynamic", "api", "json", "xhr",
	}

	urlLower := strings.ToLower(url)
	for _, pattern := range jsPatterns {
		if strings.Contains(urlLower, pattern) {
			return true
		}
	}

	return false
}

// 规则匹配
func (sc *SmartKnowledgeCrawler) findMatchingRule(url string, rules []CrawlRule) *CrawlRule {
	for _, rule := range rules {
		if matched, _ := regexp.MatchString(rule.URLPattern, url); matched {
			return &rule
		}
	}
	return nil
}

// 应用爬取规则
func (sc *SmartKnowledgeCrawler) applyRule(content, title string, links []string, rule *CrawlRule) (string, string, []string) {
	// 检查必须包含的词汇
	if len(rule.RequiredWords) > 0 {
		contentLower := strings.ToLower(content)
		hasRequired := false
		for _, word := range rule.RequiredWords {
			if strings.Contains(contentLower, strings.ToLower(word)) {
				hasRequired = true
				break
			}
		}
		if !hasRequired {
			return "", "", nil // 不符合要求，返回空内容
		}
	}

	// 检查排除词汇
	if len(rule.ExcludedWords) > 0 {
		contentLower := strings.ToLower(content)
		for _, word := range rule.ExcludedWords {
			if strings.Contains(contentLower, strings.ToLower(word)) {
				return "", "", nil // 包含排除词汇，返回空内容
			}
		}
	}

	// 检查最小词数
	if rule.MinWordCount > 0 {
		wordCount := len(strings.Fields(content))
		if wordCount < rule.MinWordCount {
			return "", "", nil // 词数不够，返回空内容
		}
	}

	return content, title, links
}

// 内容质量评分
func (sc *SmartKnowledgeCrawler) calculateQualityScore(content, title string, priorityKeywords []string) float64 {
	score := 0.0

	// 基础分数：内容长度
	contentLength := len(content)
	if contentLength > sc.config.ContentMinLength {
		score += 0.3
	}

	// 标题质量
	if len(title) > 10 && len(title) < 100 {
		score += 0.2
	}

	// 关键词匹配
	if len(priorityKeywords) > 0 {
		contentLower := strings.ToLower(content)
		titleLower := strings.ToLower(title)
		matchCount := 0

		for _, keyword := range priorityKeywords {
			keywordLower := strings.ToLower(keyword)
			if strings.Contains(contentLower, keywordLower) || strings.Contains(titleLower, keywordLower) {
				matchCount++
			}
		}

		keywordScore := float64(matchCount) / float64(len(priorityKeywords))
		score += keywordScore * 0.3
	} else {
		score += 0.3 // 没有关键词要求时给满分
	}

	// 内容结构质量
	if strings.Contains(content, "\n") && len(strings.Split(content, "\n")) > 3 {
		score += 0.1 // 有段落结构
	}

	// 避免重复内容
	sentences := strings.Split(content, "。")
	if len(sentences) > 5 {
		score += 0.1
	}

	return math.Min(score, 1.0)
}

// 语言检测
func (sc *SmartKnowledgeCrawler) detectLanguage(content string) string {
	// 简单的语言检测
	chinesePattern := regexp.MustCompile(`[\p{Han}]`)
	englishPattern := regexp.MustCompile(`[a-zA-Z]`)

	chineseCount := len(chinesePattern.FindAllString(content, -1))
	englishCount := len(englishPattern.FindAllString(content, -1))

	if chineseCount > englishCount {
		return "zh"
	} else if englishCount > 0 {
		return "en"
	}

	return "unknown"
}

// 内容提取
func (sc *SmartKnowledgeCrawler) extractContent(doc *goquery.Document) string {
	// 移除脚本和样式
	doc.Find("script, style, nav, header, footer, aside").Remove()

	// 优先提取主要内容区域
	contentSelectors := []string{
		"article",
		".content",
		".post",
		".entry",
		"main",
		"#content",
		".article-content",
		".post-content",
		".entry-content",
		".main-content",
	}

	for _, selector := range contentSelectors {
		content := strings.TrimSpace(doc.Find(selector).Text())
		if len(content) > sc.config.ContentMinLength {
			return content
		}
	}

	// 如果没有找到主要内容，提取body
	return strings.TrimSpace(doc.Find("body").Text())
}

// 链接提取
func (sc *SmartKnowledgeCrawler) extractLinks(doc *goquery.Document, baseURL string) []string {
	var links []string
	base, _ := url.Parse(baseURL)

	doc.Find("a[href]").Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		if !exists {
			return
		}

		// 解析相对URL
		linkURL, err := url.Parse(href)
		if err != nil {
			return
		}

		// 转换为绝对URL
		absoluteURL := base.ResolveReference(linkURL).String()

		// 过滤无效链接
		if sc.isValidLink(absoluteURL) {
			links = append(links, absoluteURL)
		}
	})

	return links
}

// 链接有效性检查
func (sc *SmartKnowledgeCrawler) isValidLink(link string) bool {
	// 排除特定类型的链接
	excludePatterns := []string{
		`\.(jpg|jpeg|png|gif|bmp|svg|ico|css|js|pdf|doc|docx|xls|xlsx|zip|rar)$`,
		`^mailto:`,
		`^tel:`,
		`^javascript:`,
		`^#`,
		`\?download=`,
		`/download/`,
		`/api/`,
	}

	linkLower := strings.ToLower(link)
	for _, pattern := range excludePatterns {
		if matched, _ := regexp.MatchString(pattern, linkLower); matched {
			return false
		}
	}

	return true
}

// 添加链接到队列
func (sc *SmartKnowledgeCrawler) addLinksToQueue(links []string, parentURL string, depth int, target *SmartCrawlTarget) {
	for _, link := range links {
		// 检查深度限制
		if depth > sc.config.MaxDepth {
			continue
		}

		// 检查是否已访问
		if sc.isVisited(link) {
			continue
		}

		// 检查域名限制
		if !sc.isDomainAllowed(link, target) {
			continue
		}

		pageInfo := &PageInfo{
			URL:       link,
			Depth:     depth,
			ParentURL: parentURL,
			CrawledAt: time.Now(),
		}

		select {
		case sc.urlQueue <- pageInfo:
		default:
			// 队列已满，跳过
		}
	}
}

// generateSummary 生成摘要
func (sc *SmartKnowledgeCrawler) generateSummary(content string) string {
	// 首先清理内容
	content = sc.sanitizeString(content)

	// 简单的摘要生成：取前200个字符
	content = strings.TrimSpace(content)
	if len(content) <= 200 {
		return content
	}

	// 尝试在句号处截断
	summary := content[:200]
	if lastDot := strings.LastIndex(summary, "。"); lastDot > 100 {
		summary = summary[:lastDot+3] // 包含句号
	}

	return summary + "..."
}

// extractKeywords 提取关键词
func (sc *SmartKnowledgeCrawler) extractKeywords(text string) []string {
	// 简单的关键词提取
	text = strings.ToLower(text)

	// 移除标点符号
	reg := regexp.MustCompile(`[^\p{L}\p{N}\s]+`)
	text = reg.ReplaceAllString(text, " ")

	// 分词
	words := strings.Fields(text)

	// 过滤停用词和短词
	stopWords := map[string]bool{
		"的": true, "是": true, "在": true, "有": true, "和": true, "与": true,
		"或": true, "但": true, "而": true, "了": true, "着": true, "过": true,
		"the": true, "is": true, "at": true, "which": true, "on": true, "and": true,
		"or": true, "but": true, "in": true, "with": true, "to": true, "for": true,
	}

	wordCount := make(map[string]int)
	for _, word := range words {
		if len(word) > 2 && !stopWords[word] {
			wordCount[word]++
		}
	}

	// 选择出现频率高的词作为关键词
	var keywords []string
	for word, count := range wordCount {
		if count >= 2 || len(word) > 4 {
			keywords = append(keywords, word)
		}
	}

	// 限制关键词数量
	if len(keywords) > 10 {
		keywords = keywords[:10]
	}

	return keywords
}

// sanitizeString 字符串清理（复用原有方法）
func (sc *SmartKnowledgeCrawler) sanitizeString(s string) string {
	return sc.sanitizeStringWithLength(s, 65535) // 默认TEXT字段长度
}

// sanitizeStringWithLength 清理字符串并限制长度
func (sc *SmartKnowledgeCrawler) sanitizeStringWithLength(s string, maxLength int) string {
	if s == "" {
		return s
	}

	// 1. 移除无效的UTF-8字符
	if !strings.Contains(s, "\uFFFD") {
		// 简化版本：只处理明显的问题字符
		s = strings.ReplaceAll(s, "\x00", "")
		s = strings.ReplaceAll(s, "\x01", "")
		s = strings.ReplaceAll(s, "\x02", "")
	}

	// 2. 移除控制字符（除了换行符、制表符、回车符）
	controlChars := regexp.MustCompile(`[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]`)
	s = controlChars.ReplaceAllString(s, "")

	// 3. 限制字符串长度，防止过长
	if len(s) > maxLength {
		// 安全截断，确保不会在UTF-8字符中间截断
		runes := []rune(s)
		if len(runes) > maxLength/3 { // 估算，避免中文字符被截断
			s = string(runes[:maxLength/3])
		}

		// 尝试在合适的位置截断（尽量在句号或空格处）
		if lastDot := strings.LastIndex(s, "。"); lastDot > len(s)-100 && lastDot > 0 {
			s = s[:lastDot+3]
		} else if lastSpace := strings.LastIndex(s, " "); lastSpace > len(s)-100 && lastSpace > 0 {
			s = s[:lastSpace]
		}
	}

	// 4. 清理多余的空白字符
	s = strings.TrimSpace(s)
	multiSpace := regexp.MustCompile(`\s+`)
	s = multiSpace.ReplaceAllString(s, " ")

	return s
}

// saveCrawlResult 保存爬取结果
func (sc *SmartKnowledgeCrawler) saveCrawlResult(result *CrawlResult) error {
	// 清理字符串字段，防止MySQL字符编码错误，并根据字段类型限制长度
	result.Title = sc.sanitizeStringWithLength(result.Title, 500)            // VARCHAR(500)
	result.Content = sc.sanitizeStringWithLength(result.Content, 4294967295) // LONGTEXT
	result.Summary = sc.sanitizeStringWithLength(result.Summary, 65535)      // TEXT
	result.Category = sc.sanitizeStringWithLength(result.Category, 100)      // VARCHAR(100)

	metadataJSON, _ := json.Marshal(result.Metadata)
	keywordsJSON, _ := json.Marshal(result.Keywords)

	query := `
		INSERT INTO crawl_results
		(target_id, url, title, content, summary, keywords, category, metadata, crawled_at, status)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := sc.db.Exec(query, result.TargetID, result.URL, result.Title, result.Content,
		result.Summary, keywordsJSON, result.Category, metadataJSON, result.CrawledAt, result.Status)

	return err
}

// 公开方法供测试使用
func (sc *SmartKnowledgeCrawler) GetConfig() *SmartCrawlerConfig {
	return sc.config
}

func (sc *SmartKnowledgeCrawler) IsDomainAllowed(url string, target *SmartCrawlTarget) bool {
	return sc.isDomainAllowed(url, target)
}

func (sc *SmartKnowledgeCrawler) IsValidLink(link string) bool {
	return sc.isValidLink(link)
}

func (sc *SmartKnowledgeCrawler) CalculateQualityScore(content, title string, keywords []string) float64 {
	return sc.calculateQualityScore(content, title, keywords)
}

func (sc *SmartKnowledgeCrawler) ExtractKeywords(text string) []string {
	return sc.extractKeywords(text)
}

func (sc *SmartKnowledgeCrawler) DetectLanguage(content string) string {
	return sc.detectLanguage(content)
}

func (sc *SmartKnowledgeCrawler) GetVisitedCount() int {
	sc.visitedMutex.RLock()
	defer sc.visitedMutex.RUnlock()
	return len(sc.visitedURLs)
}

func (sc *SmartKnowledgeCrawler) GetQueueSize() int {
	return len(sc.urlQueue)
}

func (sc *SmartKnowledgeCrawler) IsRunning() bool {
	return sc.running
}
