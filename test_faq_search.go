package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接数据库
	db, err := sql.Open("mysql", "root:park%123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 测试FAQ搜索功能...")

	// 测试搜索"百宝箱"
	testQueries := []string{
		"百宝箱",
		"热搜",
		"百度热搜",
		"当前热门话题",
		"微信",
	}

	for _, query := range testQueries {
		fmt.Printf("\n🔍 搜索: '%s'\n", query)

		// 1. 在learned_knowledge表中搜索
		fmt.Println("📚 在学习知识中搜索:")
		searchInLearnedKnowledge(db, query)

		// 2. 在faq表中搜索
		fmt.Println("📋 在FAQ中搜索:")
		searchInFAQ(db, query)
	}
}

func searchInLearnedKnowledge(db *sql.DB, query string) {
	searchQuery := `
		SELECT id, question, answer, confidence, status
		FROM learned_knowledge
		WHERE question LIKE CONCAT('%', ?, '%') OR answer LIKE CONCAT('%', ?, '%')
		ORDER BY confidence DESC, created_at DESC
		LIMIT 3
	`

	rows, err := db.Query(searchQuery, query, query)
	if err != nil {
		fmt.Printf("❌ 搜索失败: %v\n", err)
		return
	}
	defer rows.Close()

	found := false
	for rows.Next() {
		var id int
		var question, answer, status string
		var confidence float32

		err := rows.Scan(&id, &question, &answer, &confidence, &status)
		if err != nil {
			continue
		}

		if !found {
			found = true
		}

		// 截断长答案
		answerPreview := answer
		if len(answerPreview) > 100 {
			answerPreview = answerPreview[:100] + "..."
		}

		fmt.Printf("  ✅ ID %d (置信度 %.2f, 状态 %s): %s\n", id, confidence, status, question)
		fmt.Printf("     答案: %s\n", answerPreview)
	}

	if !found {
		fmt.Println("  ❌ 未找到相关学习知识")
	}
}

func searchInFAQ(db *sql.DB, query string) {
	searchQuery := `
		SELECT id, question, answer
		FROM faq
		WHERE question LIKE CONCAT('%', ?, '%') OR answer LIKE CONCAT('%', ?, '%')
		ORDER BY id
		LIMIT 3
	`

	rows, err := db.Query(searchQuery, query, query)
	if err != nil {
		fmt.Printf("❌ 搜索失败: %v\n", err)
		return
	}
	defer rows.Close()

	found := false
	for rows.Next() {
		var id int
		var question, answer string

		err := rows.Scan(&id, &question, &answer)
		if err != nil {
			continue
		}

		if !found {
			found = true
		}

		// 截断长答案
		answerPreview := answer
		if len(answerPreview) > 100 {
			answerPreview = answerPreview[:100] + "..."
		}

		fmt.Printf("  ✅ ID %d: %s\n", id, question)
		fmt.Printf("     答案: %s\n", answerPreview)
	}

	if !found {
		fmt.Println("  ❌ 未找到相关FAQ")
	}
}
