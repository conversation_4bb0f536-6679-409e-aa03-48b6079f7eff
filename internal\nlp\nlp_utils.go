package nlp

import (
	"strings"
	"unicode"
)

// splitIntoSentences 分句
func (ce *ContentExtractor) splitIntoSentences(content string) []string {
	// 简单的分句逻辑
	sentences := []string{}
	
	// 按标点符号分句
	delimiters := []string{"。", "！", "？", ".", "!", "?", "\n"}
	
	current := content
	for _, delimiter := range delimiters {
		parts := strings.Split(current, delimiter)
		if len(parts) > 1 {
			sentences = append(sentences, parts...)
			break
		}
	}
	
	// 如果没有找到分隔符，按长度分割
	if len(sentences) == 0 {
		maxLen := 100
		for i := 0; i < len(content); i += maxLen {
			end := i + maxLen
			if end > len(content) {
				end = len(content)
			}
			sentences = append(sentences, content[i:end])
		}
	}
	
	// 清理空句子
	cleanSentences := []string{}
	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if len(sentence) > 3 {
			cleanSentences = append(cleanSentences, sentence)
		}
	}
	
	return cleanSentences
}

// deduplicateEntities 实体去重
func (ce *ContentExtractor) deduplicateEntities(entities []Entity) []Entity {
	seen := make(map[string]bool)
	result := []Entity{}
	
	for _, entity := range entities {
		key := entity.Text + "|" + entity.Type
		if !seen[key] {
			seen[key] = true
			result = append(result, entity)
		}
	}
	
	return result
}

// tokenize 分词
func (ce *ContentExtractor) tokenize(content string) []string {
	words := []string{}
	
	// 简单的分词：按空格和标点分割
	content = strings.ReplaceAll(content, "，", " ")
	content = strings.ReplaceAll(content, "。", " ")
	content = strings.ReplaceAll(content, "！", " ")
	content = strings.ReplaceAll(content, "？", " ")
	content = strings.ReplaceAll(content, "；", " ")
	content = strings.ReplaceAll(content, "：", " ")
	content = strings.ReplaceAll(content, """, " ")
	content = strings.ReplaceAll(content, """, " ")
	content = strings.ReplaceAll(content, "'", " ")
	content = strings.ReplaceAll(content, "'", " ")
	content = strings.ReplaceAll(content, "（", " ")
	content = strings.ReplaceAll(content, "）", " ")
	content = strings.ReplaceAll(content, "【", " ")
	content = strings.ReplaceAll(content, "】", " ")
	
	fields := strings.Fields(content)
	for _, field := range fields {
		// 进一步分割中文词汇
		subWords := ce.splitChineseWords(field)
		words = append(words, subWords...)
	}
	
	return words
}

// splitChineseWords 分割中文词汇
func (ce *ContentExtractor) splitChineseWords(text string) []string {
	words := []string{}
	currentWord := ""
	
	for _, r := range text {
		if unicode.Is(unicode.Han, r) {
			currentWord += string(r)
			// 简单策略：每2-3个汉字作为一个词
			if len([]rune(currentWord)) >= 2 {
				words = append(words, currentWord)
				currentWord = ""
			}
		} else {
			if currentWord != "" {
				words = append(words, currentWord)
				currentWord = ""
			}
			if unicode.IsLetter(r) || unicode.IsDigit(r) {
				words = append(words, string(r))
			}
		}
	}
	
	if currentWord != "" {
		words = append(words, currentWord)
	}
	
	return words
}

// extractGeneralTopics 提取通用话题
func (ce *ContentExtractor) extractGeneralTopics(content string) []Topic {
	topics := []Topic{}
	lines := strings.Split(content, "\n")
	
	rank := 1
	for _, line := range lines {
		line = strings.TrimSpace(line)
		
		if len(line) >= 10 && len(line) <= 80 {
			// 检查是否包含话题特征
			if ce.hasTopicFeatures(line) {
				topic := Topic{
					Title:      line,
					Rank:       rank,
					Category:   ce.classifyTopic(line),
					Confidence: ce.calculateGeneralTopicConfidence(line),
					Keywords:   ce.extractTopicKeywords(line),
				}
				
				if topic.Confidence > 0.4 {
					topics = append(topics, topic)
					rank++
					
					if len(topics) >= 10 {
						break
					}
				}
			}
		}
	}
	
	return topics
}

// hasTopicFeatures 检查是否具有话题特征
func (ce *ContentExtractor) hasTopicFeatures(text string) bool {
	// 包含实体词汇
	entityKeywords := []string{
		"公司", "企业", "明星", "演员", "导演", "作者", "产品", "品牌",
		"电影", "电视剧", "小说", "游戏", "软件", "应用", "网站",
		"事件", "新闻", "消息", "公告", "发布", "上市", "推出",
	}
	
	for _, keyword := range entityKeywords {
		if strings.Contains(text, keyword) {
			return true
		}
	}
	
	// 主要为中文且长度适中
	chineseRatio := ce.calculateChineseRatio(text)
	return chineseRatio > 0.7
}

// calculateGeneralTopicConfidence 计算通用话题置信度
func (ce *ContentExtractor) calculateGeneralTopicConfidence(text string) float64 {
	confidence := 0.0
	
	// 长度评分
	if len(text) >= 15 && len(text) <= 40 {
		confidence += 0.3
	} else if len(text) >= 10 && len(text) <= 60 {
		confidence += 0.2
	}
	
	// 中文比例评分
	chineseRatio := ce.calculateChineseRatio(text)
	confidence += chineseRatio * 0.4
	
	// 实体特征评分
	if ce.hasTopicFeatures(text) {
		confidence += 0.3
	}
	
	return ce.min(confidence, 1.0)
}

// calculateChineseRatio 计算中文字符比例
func (ce *ContentExtractor) calculateChineseRatio(text string) float64 {
	if len(text) == 0 {
		return 0.0
	}
	
	chineseCount := 0
	totalCount := 0
	
	for _, r := range text {
		totalCount++
		if unicode.Is(unicode.Han, r) {
			chineseCount++
		}
	}
	
	return float64(chineseCount) / float64(totalCount)
}

// classifyContent 内容分类
func (ce *ContentExtractor) classifyContent(content, url string) string {
	// 基于URL的分类
	if strings.Contains(url, "top.baidu.com") {
		if strings.Contains(url, "tab=realtime") {
			return "实时热点"
		}
		if strings.Contains(url, "tab=novel") {
			return "小说榜单"
		}
		if strings.Contains(url, "tab=movie") {
			return "电影榜单"
		}
		if strings.Contains(url, "tab=teleplay") {
			return "电视剧榜单"
		}
		return "热搜榜单"
	}
	
	// 基于内容的分类
	contentLower := strings.ToLower(content)
	
	if strings.Contains(contentLower, "新闻") || strings.Contains(contentLower, "消息") {
		return "新闻资讯"
	}
	if strings.Contains(contentLower, "小说") || strings.Contains(contentLower, "作者") {
		return "文学作品"
	}
	if strings.Contains(contentLower, "电影") || strings.Contains(contentLower, "导演") {
		return "影视娱乐"
	}
	if strings.Contains(contentLower, "科技") || strings.Contains(contentLower, "技术") {
		return "科技资讯"
	}
	if strings.Contains(contentLower, "体育") || strings.Contains(contentLower, "比赛") {
		return "体育赛事"
	}
	
	return "综合内容"
}

// analyzeSentiment 情感分析
func (ce *ContentExtractor) analyzeSentiment(content string) string {
	positiveWords := []string{
		"好", "棒", "优秀", "精彩", "成功", "胜利", "喜欢", "爱",
		"开心", "高兴", "满意", "赞", "支持", "推荐", "优质",
	}
	
	negativeWords := []string{
		"坏", "差", "失败", "糟糕", "讨厌", "恨", "愤怒", "生气",
		"失望", "不满", "批评", "反对", "抗议", "问题", "错误",
	}
	
	positiveCount := 0
	negativeCount := 0
	
	contentLower := strings.ToLower(content)
	
	for _, word := range positiveWords {
		positiveCount += strings.Count(contentLower, word)
	}
	
	for _, word := range negativeWords {
		negativeCount += strings.Count(contentLower, word)
	}
	
	if positiveCount > negativeCount {
		return "积极"
	} else if negativeCount > positiveCount {
		return "消极"
	} else {
		return "中性"
	}
}

// generateSummary 生成摘要
func (ce *ContentExtractor) generateSummary(content string, topics []Topic) string {
	// 如果有话题，基于话题生成摘要
	if len(topics) > 0 {
		topicTitles := []string{}
		for i, topic := range topics {
			if i >= 5 { // 最多使用前5个话题
				break
			}
			topicTitles = append(topicTitles, topic.Title)
		}
		return strings.Join(topicTitles, "；")
	}
	
	// 否则提取前几句作为摘要
	sentences := ce.splitIntoSentences(content)
	summaryParts := []string{}
	
	for i, sentence := range sentences {
		if i >= 3 { // 最多3句
			break
		}
		if len(sentence) >= 10 && len(sentence) <= 100 {
			summaryParts = append(summaryParts, sentence)
		}
	}
	
	summary := strings.Join(summaryParts, "。")
	
	// 限制摘要长度
	if len(summary) > 200 {
		summary = summary[:200] + "..."
	}
	
	return summary
}

// calculateConfidence 计算整体置信度
func (ce *ContentExtractor) calculateConfidence(extracted *ExtractedContent) float64 {
	confidence := 0.0
	
	// 标题质量评分
	if len(extracted.Title) >= 5 && len(extracted.Title) <= 50 {
		confidence += 0.2
	}
	
	// 实体数量评分
	if len(extracted.Entities) >= 3 {
		confidence += 0.2
	} else if len(extracted.Entities) >= 1 {
		confidence += 0.1
	}
	
	// 关键词数量评分
	if len(extracted.Keywords) >= 5 {
		confidence += 0.2
	} else if len(extracted.Keywords) >= 2 {
		confidence += 0.1
	}
	
	// 话题数量评分
	if len(extracted.Topics) >= 5 {
		confidence += 0.3
	} else if len(extracted.Topics) >= 2 {
		confidence += 0.2
	} else if len(extracted.Topics) >= 1 {
		confidence += 0.1
	}
	
	// 摘要质量评分
	if len(extracted.Summary) >= 20 {
		confidence += 0.1
	}
	
	return ce.min(confidence, 1.0)
}

// extractStructuredData 提取结构化数据
func (ce *ContentExtractor) extractStructuredData(content, url string) map[string]string {
	data := make(map[string]string)
	
	// 提取时间信息
	if timeMatches := ce.patterns["time"].FindStringSubmatch(content); len(timeMatches) > 1 {
		data["time"] = timeMatches[1]
	}
	
	// 提取数字信息
	numbers := ce.patterns["number"].FindAllString(content, -1)
	if len(numbers) > 0 {
		data["numbers"] = strings.Join(numbers[:ce.min(len(numbers), 5)], ",")
	}
	
	// 提取URL类型信息
	if strings.Contains(url, "top.baidu.com") {
		data["source"] = "百度热搜"
		if strings.Contains(url, "tab=") {
			parts := strings.Split(url, "tab=")
			if len(parts) > 1 {
				tab := strings.Split(parts[1], "&")[0]
				data["tab"] = tab
			}
		}
	}
	
	return data
}

// min 辅助函数：返回两个数的最小值
func (ce *ContentExtractor) min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// min 辅助函数：返回两个浮点数的最小值
func (ce *ContentExtractor) minFloat(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}
