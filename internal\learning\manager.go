package learning

import (
	"database/sql"
	"time"

	"faq-system/internal/logger"
)

// Manager 学习系统管理器
type Manager struct {
	db               *sql.DB
	engine           *LearningEngine
	optimizer        *Optimizer
	evolutionEngine  *EvolutionEngine
	knowledgeLearner *KnowledgeLearner
	api              *LearningAPI
	ticker           *time.Ticker
	stopChan         chan bool
	isRunning        bool
}

// NewManager 创建学习系统管理器
func NewManager(db *sql.DB) *Manager {
	engine := NewLearningEngine(db)
	optimizer := NewOptimizer(db, engine)
	evolutionEngine := NewEvolutionEngine(db, &LearningConfig{})
	knowledgeLearner := NewKnowledgeLearner(db, nil, nil) // 向量存储和嵌入客户端稍后设置
	api := NewLearningAPI(engine)

	return &Manager{
		db:               db,
		engine:           engine,
		optimizer:        optimizer,
		evolutionEngine:  evolutionEngine,
		knowledgeLearner: knowledgeLearner,
		api:              api,
		stopChan:         make(chan bool),
	}
}

// GetEngine 获取学习引擎
func (m *Manager) GetEngine() *LearningEngine {
	return m.engine
}

// GetEvolutionEngine 获取进化引擎
func (m *Manager) GetEvolutionEngine() *EvolutionEngine {
	return m.evolutionEngine
}

// GetKnowledgeLearner 获取知识学习器
func (m *Manager) GetKnowledgeLearner() *KnowledgeLearner {
	return m.knowledgeLearner
}

// SetEmbeddingComponents 设置嵌入相关组件
func (m *Manager) SetEmbeddingComponents(vectorStore interface{}, embedClient interface{}) {
	// 由于循环导入问题，这个方法暂时不实现
	// 向量功能是可选的，不影响基本的知识学习功能
}

// GetAPI 获取学习API
func (m *Manager) GetAPI() *LearningAPI {
	return m.api
}

// Start 启动学习系统
func (m *Manager) Start() error {
	if m.isRunning {
		return nil
	}

	logger.Info("Starting learning system...")

	// 获取更新间隔配置
	interval := m.engine.getIntConfig("pattern_update_interval", 3600)
	m.ticker = time.NewTicker(time.Duration(interval) * time.Second)

	// 启动后台学习任务
	go m.runLearningTasks()

	m.isRunning = true
	logger.Infof("Learning system started with update interval: %d seconds", interval)
	return nil
}

// Stop 停止学习系统
func (m *Manager) Stop() {
	if !m.isRunning {
		return
	}

	logger.Info("Stopping learning system...")

	// 停止定时器
	if m.ticker != nil {
		m.ticker.Stop()
	}

	// 发送停止信号
	select {
	case m.stopChan <- true:
	default:
	}

	m.isRunning = false
	logger.Info("Learning system stopped")
}

// runLearningTasks 运行学习任务
func (m *Manager) runLearningTasks() {
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("Learning task panic: %v", r)
		}
	}()

	for {
		select {
		case <-m.ticker.C:
			m.performLearningCycle()
		case <-m.stopChan:
			return
		}
	}
}

// performLearningCycle 执行学习周期
func (m *Manager) performLearningCycle() {
	logger.Info("🔄 开始智能学习周期...")

	// 1. 分析查询模式
	if err := m.engine.AnalyzeQueryPatterns(); err != nil {
		logger.Errorf("Query pattern analysis failed: %v", err)
	}

	// 2. 分析用户思维模式 (新增)
	if err := m.evolutionEngine.AnalyzeUserThinking(); err != nil {
		logger.Errorf("User thinking analysis failed: %v", err)
	}

	// 3. 优化匹配得分
	if err := m.optimizer.OptimizeMatchingScores(); err != nil {
		logger.Errorf("Matching score optimization failed: %v", err)
	}

	// 4. 应用进化改进 (新增)
	if err := m.evolutionEngine.ApplyEvolutionaryImprovements(); err != nil {
		logger.Errorf("Evolutionary improvements failed: %v", err)
	}

	// 5. 清理过期数据
	if err := m.cleanupOldData(); err != nil {
		logger.Errorf("Data cleanup failed: %v", err)
	}

	// 6. 更新统计信息
	if err := m.updateStatistics(); err != nil {
		logger.Errorf("Statistics update failed: %v", err)
	}

	logger.Info("✅ 智能学习周期完成")
}

// cleanupOldData 清理过期数据
func (m *Manager) cleanupOldData() error {
	// 清理超过30天的查询记录（保留有反馈的记录）
	_, err := m.db.Exec(`
		DELETE uq FROM user_queries uq
		LEFT JOIN user_feedback uf ON uq.id = uf.query_id
		WHERE uq.created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
		  AND uf.id IS NULL
	`)
	if err != nil {
		return err
	}

	// 清理超过7天的行为记录
	_, err = m.db.Exec(`
		DELETE FROM user_behaviors 
		WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
	`)
	if err != nil {
		return err
	}

	// 清理低置信度的学习模式
	_, err = m.db.Exec(`
		DELETE FROM learning_patterns 
		WHERE confidence < 0.3 
		  AND usage_count < 5
		  AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
	`)
	if err != nil {
		return err
	}

	logger.Info("Old data cleanup completed")
	return nil
}

// updateStatistics 更新统计信息
func (m *Manager) updateStatistics() error {
	// 更新FAQ性能统计中的查询计数
	_, err := m.db.Exec(`
		UPDATE faq_performance fp
		JOIN (
			SELECT sr.matched_faq_id, COUNT(*) as query_count
			FROM system_responses sr
			WHERE sr.matched_faq_id IS NOT NULL
			  AND sr.created_at > DATE_SUB(NOW(), INTERVAL 1 DAY)
			GROUP BY sr.matched_faq_id
		) daily_stats ON fp.faq_id = daily_stats.matched_faq_id
		SET fp.query_count = fp.query_count + daily_stats.query_count,
		    fp.updated_at = NOW()
	`)
	if err != nil {
		return err
	}

	logger.Info("Statistics update completed")
	return nil
}

// GetLearningStatus 获取学习系统状态
func (m *Manager) GetLearningStatus() map[string]interface{} {
	status := map[string]interface{}{
		"running":    m.isRunning,
		"last_cycle": time.Now().Format("2006-01-02 15:04:05"),
	}

	// 获取学习指标
	if metrics, err := m.engine.GetCollector().GetLearningMetrics(); err == nil {
		status["metrics"] = metrics
	}

	// 获取配置信息
	status["config"] = m.engine.config

	return status
}

// TriggerLearningAnalysis 手动触发学习分析
func (m *Manager) TriggerLearningAnalysis() error {
	logger.Info("Manual learning analysis triggered")

	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("Manual learning analysis panic: %v", r)
			}
		}()

		m.performLearningCycle()
	}()

	return nil
}

// UpdateConfig 更新学习配置
func (m *Manager) UpdateConfig(key, value string) error {
	_, err := m.db.Exec(`
		UPDATE learning_config 
		SET config_value = ?, updated_at = NOW() 
		WHERE config_key = ?
	`, value, key)

	if err != nil {
		return err
	}

	// 重新加载配置
	m.engine.loadConfig()

	// 如果更新的是更新间隔，重启定时器
	if key == "pattern_update_interval" && m.isRunning {
		if m.ticker != nil {
			m.ticker.Stop()
		}

		interval := m.engine.getIntConfig("pattern_update_interval", 3600)
		m.ticker = time.NewTicker(time.Duration(interval) * time.Second)

		logger.Infof("Learning update interval changed to %d seconds", interval)
	}

	return nil
}

// GetRecentActivity 获取最近的学习活动
func (m *Manager) GetRecentActivity(limit int) ([]map[string]interface{}, error) {
	if limit <= 0 {
		limit = 10
	}

	query := `
		SELECT 'query' as type, uq.query_text as content, uq.created_at as timestamp
		FROM user_queries uq
		ORDER BY uq.created_at DESC
		LIMIT ?
	`

	rows, err := m.db.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var activities []map[string]interface{}
	for rows.Next() {
		var actType, content string
		var timestamp time.Time

		if err := rows.Scan(&actType, &content, &timestamp); err != nil {
			continue
		}

		activities = append(activities, map[string]interface{}{
			"type":      actType,
			"content":   content,
			"timestamp": timestamp.Format("2006-01-02 15:04:05"),
		})
	}

	return activities, nil
}
