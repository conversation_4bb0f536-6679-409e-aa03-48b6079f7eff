package crawler

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"faq-system/internal/learning"

	"github.com/PuerkitoBio/goquery"
	"github.com/chromedp/chromedp"
)

// SmartCrawlerConfig 智能爬虫配置
type SmartCrawlerConfig struct {
	MaxDepth            int           `json:"max_depth"`             // 最大爬取深度
	MaxPagesPerDomain   int           `json:"max_pages_per_domain"`  // 每个域名最大页面数
	ContentMinLength    int           `json:"content_min_length"`    // 内容最小长度
	ContentQualityScore float64       `json:"content_quality_score"` // 内容质量阈值
	EnableJSRendering   bool          `json:"enable_js_rendering"`   // 启用JavaScript渲染
	RespectRobotsTxt    bool          `json:"respect_robots_txt"`    // 遵守robots.txt
	UserAgentRotation   []string      `json:"user_agent_rotation"`   // User-Agent轮换
	ProxyList           []string      `json:"proxy_list"`            // 代理列表
	RequestDelay        time.Duration `json:"request_delay"`         // 请求延迟
	RandomDelay         bool          `json:"random_delay"`          // 随机延迟
	MaxConcurrency      int           `json:"max_concurrency"`       // 最大并发数
	Timeout             time.Duration `json:"timeout"`               // 请求超时

	// 链接数量限制
	MaxLinksPerPage  int    `json:"max_links_per_page"`  // 每页最大链接数
	MaxLinksPerDepth int    `json:"max_links_per_depth"` // 每层最大链接数
	MaxTotalLinks    int    `json:"max_total_links"`     // 总最大链接数
	LinkPriorityMode string `json:"link_priority_mode"`  // 链接优先级模式: quality, keyword, random
}

// CrawlRule 爬取规则
type CrawlRule struct {
	URLPattern      string            `json:"url_pattern"`      // URL匹配模式
	FollowLinks     []string          `json:"follow_links"`     // 要跟踪的链接选择器
	IgnoreLinks     []string          `json:"ignore_links"`     // 要忽略的链接选择器
	ContentSelector string            `json:"content_selector"` // 内容选择器
	TitleSelector   string            `json:"title_selector"`   // 标题选择器
	MetaSelectors   map[string]string `json:"meta_selectors"`   // 元数据选择器
	RequiredWords   []string          `json:"required_words"`   // 必须包含的词汇
	ExcludedWords   []string          `json:"excluded_words"`   // 排除的词汇
	MinWordCount    int               `json:"min_word_count"`   // 最小词数
	MaxDepth        int               `json:"max_depth"`        // 此规则的最大深度
}

// SmartCrawlTarget 智能爬取目标
type SmartCrawlTarget struct {
	*CrawlTarget
	Rules            []CrawlRule `json:"rules"`             // 爬取规则
	SeedURLs         []string    `json:"seed_urls"`         // 种子URL
	AllowedDomains   []string    `json:"allowed_domains"`   // 允许的域名
	BlockedDomains   []string    `json:"blocked_domains"`   // 禁止的域名
	CrawlStrategy    string      `json:"crawl_strategy"`    // 爬取策略: breadth_first, depth_first, smart
	PriorityKeywords []string    `json:"priority_keywords"` // 优先关键词
}

// PageInfo 页面信息
type PageInfo struct {
	URL            string                 `json:"url"`
	Title          string                 `json:"title"`
	Content        string                 `json:"content"`
	Links          []string               `json:"links"`
	Depth          int                    `json:"depth"`
	ParentURL      string                 `json:"parent_url"`
	QualityScore   float64                `json:"quality_score"`
	ContentType    string                 `json:"content_type"`
	Language       string                 `json:"language"`
	Metadata       map[string]interface{} `json:"metadata"`
	CrawledAt      time.Time              `json:"crawled_at"`
	ProcessingTime time.Duration          `json:"processing_time"`
}

// SmartKnowledgeCrawler 智能知识爬虫
type SmartKnowledgeCrawler struct {
	db               *sql.DB
	config           *SmartCrawlerConfig
	knowledgeLearner *learning.KnowledgeLearner
	httpClient       *http.Client

	// 爬取状态管理
	visitedURLs  map[string]bool
	urlQueue     chan *PageInfo
	resultQueue  chan *PageInfo
	visitedMutex sync.RWMutex

	// 域名管理
	domainPageCount map[string]int
	domainMutex     sync.RWMutex

	// 反爬虫对抗
	userAgentIndex  int
	proxyIndex      int
	lastRequestTime map[string]time.Time
	requestMutex    sync.RWMutex

	// Chrome实例（用于JS渲染）
	chromeCtx    context.Context
	chromeCancel context.CancelFunc

	// 控制
	stopChan chan struct{}
	wg       sync.WaitGroup
	running  bool

	// 多目标支持
	activeTargets map[int]bool
	targetsMutex  sync.RWMutex

	// 链接数量统计
	totalLinksProcessed int
	depthLinkCount      map[int]int // 每层链接数量统计
	linkCountMutex      sync.RWMutex
}

// NewSmartKnowledgeCrawler 创建智能知识爬虫
func NewSmartKnowledgeCrawler(db *sql.DB, knowledgeLearner *learning.KnowledgeLearner) *SmartKnowledgeCrawler {
	config := &SmartCrawlerConfig{
		MaxDepth:            5,
		MaxPagesPerDomain:   100,
		ContentMinLength:    200,
		ContentQualityScore: 0.6,
		EnableJSRendering:   true,
		RespectRobotsTxt:    true,
		UserAgentRotation: []string{
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		},
		RequestDelay:   time.Second * 2,
		RandomDelay:    true,
		MaxConcurrency: 3,
		Timeout:        time.Second * 30,

		// 链接数量限制配置
		MaxLinksPerPage:  50,        // 每页最多提取50个链接
		MaxLinksPerDepth: 200,       // 每层最多爬取200个链接
		MaxTotalLinks:    1000,      // 总共最多爬取1000个链接
		LinkPriorityMode: "quality", // 优先级模式：quality, keyword, random
	}

	httpClient := &http.Client{
		Timeout: config.Timeout,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	return &SmartKnowledgeCrawler{
		db:               db,
		config:           config,
		knowledgeLearner: knowledgeLearner,
		httpClient:       httpClient,
		visitedURLs:      make(map[string]bool),
		urlQueue:         make(chan *PageInfo, 1000),
		resultQueue:      make(chan *PageInfo, 1000),
		domainPageCount:  make(map[string]int),
		lastRequestTime:  make(map[string]time.Time),
		stopChan:         make(chan struct{}),
		activeTargets:    make(map[int]bool),
		depthLinkCount:   make(map[int]int),
	}
}

// StartSmartCrawl 启动智能爬取
func (sc *SmartKnowledgeCrawler) StartSmartCrawl(target *SmartCrawlTarget) error {
	// 检查目标是否已在运行
	sc.targetsMutex.Lock()
	if sc.activeTargets[target.ID] {
		sc.targetsMutex.Unlock()
		return fmt.Errorf("目标 %s 已经在智能爬取中", target.Name)
	}
	sc.activeTargets[target.ID] = true
	sc.targetsMutex.Unlock()

	log.Printf("🧠 启动智能爬取: %s (ID: %d)", target.Name, target.ID)

	// 为每个目标启动独立的爬取任务
	go sc.startTargetCrawl(target)

	log.Printf("✅ 智能爬取任务已启动: %s", target.Name)
	return nil
}

// StopSmartCrawl 停止智能爬取
func (sc *SmartKnowledgeCrawler) StopSmartCrawl() {
	if !sc.running {
		return
	}

	log.Println("🛑 停止智能爬虫...")
	sc.running = false
	close(sc.stopChan)

	// 关闭Chrome实例
	if sc.chromeCancel != nil {
		sc.chromeCancel()
	}

	sc.wg.Wait()
	log.Println("✅ 智能爬虫已停止")
}

// initChrome 初始化Chrome实例
func (sc *SmartKnowledgeCrawler) initChrome() error {
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.Flag("disable-extensions", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.UserAgent(sc.config.UserAgentRotation[0]),
	)

	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	sc.chromeCtx, sc.chromeCancel = chromedp.NewContext(allocCtx)

	// 测试Chrome是否可用
	err := chromedp.Run(sc.chromeCtx, chromedp.Navigate("about:blank"))
	if err != nil {
		cancel()
		return fmt.Errorf("Chrome测试失败: %v", err)
	}

	log.Println("✅ Chrome实例初始化成功")
	return nil
}

// crawlWorker 爬取工作协程
func (sc *SmartKnowledgeCrawler) crawlWorker(target *SmartCrawlTarget) {
	defer sc.wg.Done()

	for {
		select {
		case <-sc.stopChan:
			return
		case pageInfo := <-sc.urlQueue:
			if pageInfo == nil {
				continue
			}

			// 检查是否已访问
			if sc.isVisited(pageInfo.URL) {
				continue
			}

			// 检查域名限制
			if !sc.isDomainAllowed(pageInfo.URL, target) {
				continue
			}

			// 检查深度限制
			if pageInfo.Depth > sc.config.MaxDepth {
				continue
			}

			// 检查域名页面数限制
			domain := sc.extractDomain(pageInfo.URL)
			if sc.getDomainPageCount(domain) >= sc.config.MaxPagesPerDomain {
				continue
			}

			// 执行爬取
			result, err := sc.crawlPage(pageInfo, target)
			if err != nil {
				log.Printf("❌ 爬取失败 %s: %v", pageInfo.URL, err)
				continue
			}

			// 标记为已访问
			sc.markVisited(pageInfo.URL)
			sc.incrementDomainPageCount(domain)

			// 发送结果到处理队列
			select {
			case sc.resultQueue <- result:
			default:
				log.Printf("⚠️ 结果队列已满，丢弃结果: %s", result.URL)
			}

			// 请求延迟
			sc.applyRequestDelay(domain)
		}
	}
}

// crawlPage 爬取单个页面
func (sc *SmartKnowledgeCrawler) crawlPage(pageInfo *PageInfo, target *SmartCrawlTarget) (*PageInfo, error) {
	startTime := time.Now()

	// 详细日志：开始爬取
	log.Printf("🕷️ [第%d层] 开始爬取: %s", pageInfo.Depth, pageInfo.URL)
	if pageInfo.ParentURL != "" {
		log.Printf("   └─ 来源页面: %s", pageInfo.ParentURL)
	}

	log.Printf("🕷️ 爬取页面: %s (深度: %d)", pageInfo.URL, pageInfo.Depth)

	var content, title string
	var links []string
	var err error

	// 根据配置选择爬取方式
	if sc.config.EnableJSRendering && sc.needsJSRendering(pageInfo.URL) {
		log.Printf("   🌐 使用Chrome渲染爬取")
		content, title, links, err = sc.crawlWithChrome(pageInfo.URL, target)
	} else {
		log.Printf("   📄 使用HTTP客户端爬取")
		content, title, links, err = sc.crawlWithHTTP(pageInfo.URL, target)
	}

	if err != nil {
		log.Printf("   ❌ 爬取失败: %v", err)
		return nil, err
	}

	log.Printf("   ✅ 爬取成功: 标题长度=%d, 内容长度=%d, 链接数=%d",
		len(title), len(content), len(links))

	// 应用爬取规则
	rule := sc.findMatchingRule(pageInfo.URL, target.Rules)
	if rule != nil {
		content, title, links = sc.applyRule(content, title, links, rule)
	}

	// 计算内容质量分数
	qualityScore := sc.calculateQualityScore(content, title, target.PriorityKeywords)
	log.Printf("   📊 内容质量评分: %.2f (阈值: %.2f)", qualityScore, sc.config.ContentQualityScore)

	// 内容质量检查：只有在最大深度时才严格过滤，否则继续深入爬取
	shouldSaveContent := qualityScore >= sc.config.ContentQualityScore
	if !shouldSaveContent && pageInfo.Depth >= sc.config.MaxDepth {
		// 已达到最大深度且质量不达标，放弃
		log.Printf("   ❌ 最大深度内容质量不达标，放弃: %.2f < %.2f", qualityScore, sc.config.ContentQualityScore)
		return nil, fmt.Errorf("最大深度内容质量不达标: %.2f < %.2f", qualityScore, sc.config.ContentQualityScore)
	}

	// 如果质量不达标但未达到最大深度，仍然提取链接继续爬取
	if !shouldSaveContent {
		log.Printf("   ⚠️ 第%d层内容质量不达标(%.2f < %.2f)，继续深入爬取",
			pageInfo.Depth, qualityScore, sc.config.ContentQualityScore)
	} else {
		log.Printf("   ✅ 内容质量达标，将保存内容")
	}

	// 构建结果
	result := &PageInfo{
		URL:          pageInfo.URL,
		Title:        title,
		Content:      content,
		Links:        links,
		Depth:        pageInfo.Depth,
		ParentURL:    pageInfo.ParentURL,
		QualityScore: qualityScore,
		ContentType:  "text/html",
		Language:     sc.detectLanguage(content),
		Metadata: map[string]interface{}{
			"should_save": shouldSaveContent, // 标记是否应该保存内容
		},
		CrawledAt:      time.Now(),
		ProcessingTime: time.Since(startTime),
	}

	// 使用新的链接过滤和限制逻辑
	log.Printf("   🔗 发现 %d 个原始链接", len(links))
	if len(links) > 0 {
		log.Printf("   📋 原始链接列表:")
		for i, link := range links[:min(10, len(links))] { // 只显示前10个
			log.Printf("     %d. %s", i+1, link)
		}
		if len(links) > 10 {
			log.Printf("     ... 还有 %d 个链接", len(links)-10)
		}
	}

	filteredLinks := sc.filterAndLimitLinks(links, pageInfo.Depth+1, target)

	// 添加过滤后的链接到队列
	addedCount := 0
	for _, link := range filteredLinks {
		newPageInfo := &PageInfo{
			URL:       link,
			Depth:     pageInfo.Depth + 1,
			ParentURL: pageInfo.URL,
			CrawledAt: time.Now(),
		}

		// 检查是否已访问
		sc.visitedMutex.RLock()
		visited := sc.visitedURLs[link]
		sc.visitedMutex.RUnlock()

		if !visited {
			select {
			case sc.urlQueue <- newPageInfo:
				addedCount++
				log.Printf("   ➕ 添加到队列: %s", link)
			default:
				log.Printf("   ⚠️ URL队列已满，跳过链接: %s", link)
			}
		} else {
			log.Printf("   ⏭️ 已访问，跳过: %s", link)
		}
	}

	log.Printf("   📊 链接处理结果: 发现%d个 → 过滤%d个 → 添加%d个到队列",
		len(links), len(filteredLinks), addedCount)

	return result, nil
}

// crawlWithHTTP 使用HTTP客户端爬取
func (sc *SmartKnowledgeCrawler) crawlWithHTTP(url string, target *SmartCrawlTarget) (string, string, []string, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", "", nil, err
	}

	// 设置随机User-Agent
	userAgent := sc.getRandomUserAgent()
	req.Header.Set("User-Agent", userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := sc.httpClient.Do(req)
	if err != nil {
		return "", "", nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", "", nil, fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return "", "", nil, err
	}

	// 提取标题
	title := strings.TrimSpace(doc.Find("title").First().Text())

	// 提取内容
	content := sc.extractContent(doc)

	// 提取链接
	links := sc.extractLinks(doc, url)

	return content, title, links, nil
}

// crawlWithChrome 使用Chrome爬取（支持JavaScript）
func (sc *SmartKnowledgeCrawler) crawlWithChrome(url string, target *SmartCrawlTarget) (string, string, []string, error) {
	ctx, cancel := context.WithTimeout(sc.chromeCtx, sc.config.Timeout)
	defer cancel()

	var title, content string
	var links []string

	err := chromedp.Run(ctx,
		chromedp.Navigate(url),
		chromedp.WaitVisible("body", chromedp.ByQuery),
		chromedp.Title(&title),
		chromedp.Text("body", &content, chromedp.ByQuery),
		chromedp.Evaluate(`
			(() => {
				try {
					return Array.from(document.querySelectorAll('a[href]'))
						.map(a => {
							const href = a.href;
							if (href && (href.startsWith('http://') || href.startsWith('https://'))) {
								return href;
							}
							return null;
						})
						.filter(href => href !== null);
				} catch(e) {
					console.log('Link extraction error:', e);
					return [];
				}
			})()
		`, &links),
	)

	if err != nil {
		log.Printf("   ❌ Chrome爬取失败: %v", err)
		return "", "", nil, fmt.Errorf("Chrome爬取失败: %v", err)
	}

	// 内容清理和验证
	content = sc.sanitizeContent(content)
	title = sc.sanitizeContent(title)

	log.Printf("   🔗 Chrome提取到 %d 个链接", len(links))
	if len(links) > 0 {
		log.Printf("   📋 前5个链接示例:")
		for i, link := range links[:min(5, len(links))] {
			log.Printf("     %d. %s", i+1, link)
		}
	}

	return content, title, links, nil
}

// resultProcessor 结果处理协程
func (sc *SmartKnowledgeCrawler) resultProcessor(target *SmartCrawlTarget) {
	defer sc.wg.Done()

	for {
		select {
		case <-sc.stopChan:
			return
		case result := <-sc.resultQueue:
			if result == nil {
				continue
			}

			// 处理爬取结果
			if err := sc.processResult(result, target); err != nil {
				log.Printf("❌ 处理结果失败 %s: %v", result.URL, err)
			}
		}
	}
}

// processResult 处理单个结果
func (sc *SmartKnowledgeCrawler) processResult(result *PageInfo, target *SmartCrawlTarget) error {
	// 检查是否应该保存内容
	shouldSave, ok := result.Metadata["should_save"].(bool)
	if !ok || !shouldSave {
		log.Printf("⏭️ 跳过低质量内容: %s (质量分数: %.2f)", result.URL, result.QualityScore)
		return nil // 不保存，但不报错
	}

	log.Printf("📝 处理高质量结果: %s (质量分数: %.2f)", result.URL, result.QualityScore)

	// 清理所有文本字段，确保数据库兼容性
	cleanTitle := sc.sanitizeContent(result.Title)
	cleanContent := sc.sanitizeContent(result.Content)
	cleanURL := sc.sanitizeContent(result.URL)

	log.Printf("   🧹 内容清理: 标题 %d→%d 字符, 内容 %d→%d 字符",
		len(result.Title), len(cleanTitle), len(result.Content), len(cleanContent))

	// 转换为CrawlResult格式
	crawlResult := &CrawlResult{
		TargetID:  target.ID,
		URL:       cleanURL,
		Title:     cleanTitle,
		Content:   cleanContent,
		Summary:   sc.generateSummary(cleanContent),
		Keywords:  sc.extractKeywords(cleanTitle + " " + cleanContent),
		Category:  target.Category,
		CrawledAt: result.CrawledAt,
		Status:    "processed",
		Metadata: map[string]interface{}{
			"depth":           result.Depth,
			"parent_url":      result.ParentURL,
			"quality_score":   result.QualityScore,
			"content_type":    result.ContentType,
			"language":        result.Language,
			"processing_time": result.ProcessingTime.Milliseconds(),
			"links_found":     len(result.Links),
		},
	}

	// 保存爬取结果
	if err := sc.saveCrawlResult(crawlResult); err != nil {
		return fmt.Errorf("保存爬取结果失败: %v", err)
	}

	// 异步提取并保存知识（避免阻塞爬取）
	if sc.knowledgeLearner != nil && len(result.Content) > sc.config.ContentMinLength {
		go func() {
			knowledge := sc.extractKnowledgeFromContent(result, target)
			if knowledge != nil {
				if err := sc.knowledgeLearner.SaveKnowledge(knowledge); err != nil {
					log.Printf("❌ 保存知识失败: %v", err)
				} else {
					log.Printf("✅ 知识保存成功: %s", knowledge.Question)
				}
			}
		}()
	}

	return nil
}

// extractKnowledgeFromContent 从内容中提取知识
func (sc *SmartKnowledgeCrawler) extractKnowledgeFromContent(result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	content := result.Content
	title := result.Title

	// 1. 如果标题是问句形式，直接使用
	if strings.Contains(title, "什么是") || strings.Contains(title, "如何") ||
		strings.Contains(title, "怎么") || strings.Contains(title, "为什么") ||
		strings.HasSuffix(title, "?") || strings.HasSuffix(title, "？") {

		return sc.createKnowledge(title, sc.generateSummary(content), result, target, "question_title")
	}

	// 2. 尝试从内容中提取问答对
	if qa := sc.extractQAFromContent(content, title); qa != nil {
		qa.Source = "crawler"
		qa.LearnedFrom = "smart_crawler"
		qa.Status = "approved"
		qa.Category = target.Category
		qa.Context = fmt.Sprintf("从网站爬取: %s", result.URL)
		qa.Confidence = float32(result.QualityScore)
		qa.CreatedAt = time.Now()
		qa.Metadata = map[string]interface{}{
			"source_url":      result.URL,
			"crawled_at":      result.CrawledAt,
			"depth":           result.Depth,
			"quality_score":   result.QualityScore,
			"content_length":  len(content),
			"language":        result.Language,
			"extraction_type": "qa_content",
		}
		return qa
	}

	// 3. 基于标题和内容生成通用知识
	if len(title) > 0 && len(content) > 100 {
		question := sc.generateQuestionFromTitle(title, target)
		if question != "" {
			answer := sc.generateSummary(content)
			if len(answer) > 20 {
				return sc.createKnowledge(question, answer, result, target, "generated_content")
			}
		}
	}

	// 4. 特殊网站处理
	if specialKnowledge := sc.extractSpecialSiteKnowledge(result, target); specialKnowledge != nil {
		return specialKnowledge
	}

	return nil
}

// extractQAFromContent 从内容中提取问答对
func (sc *SmartKnowledgeCrawler) extractQAFromContent(content, title string) *learning.LearnedKnowledge {
	// 查找问答模式
	qaPatterns := []struct {
		questionPattern string
		answerPattern   string
	}{
		{`问[:：]\s*(.+?)[\n\r]`, `答[:：]\s*(.+?)(?:\n\n|\r\r|$)`},
		{`Q[:：]\s*(.+?)[\n\r]`, `A[:：]\s*(.+?)(?:\n\n|\r\r|$)`},
		{`\d+[\.、]\s*(.+?\?)`, `(.+?)(?:\n\d+|\n[A-Z]|$)`},
	}

	for _, pattern := range qaPatterns {
		questionRegex := regexp.MustCompile(pattern.questionPattern)
		answerRegex := regexp.MustCompile(pattern.answerPattern)

		questionMatches := questionRegex.FindAllStringSubmatch(content, -1)
		answerMatches := answerRegex.FindAllStringSubmatch(content, -1)

		if len(questionMatches) > 0 && len(answerMatches) > 0 {
			question := strings.TrimSpace(questionMatches[0][1])
			answer := strings.TrimSpace(answerMatches[0][1])

			if len(question) > 5 && len(answer) > 10 {
				return &learning.LearnedKnowledge{
					Question: question,
					Answer:   answer,
				}
			}
		}
	}

	return nil
}

// startTargetCrawl 为单个目标启动独立的爬取任务
func (sc *SmartKnowledgeCrawler) startTargetCrawl(target *SmartCrawlTarget) {
	defer func() {
		// 清理目标状态
		sc.targetsMutex.Lock()
		delete(sc.activeTargets, target.ID)
		sc.targetsMutex.Unlock()
		log.Printf("🏁 智能爬取完成: %s", target.Name)
	}()

	// 初始化Chrome实例（如果启用JS渲染且尚未初始化）
	if sc.config.EnableJSRendering && sc.chromeCtx == nil {
		if err := sc.initChrome(); err != nil {
			log.Printf("⚠️ Chrome初始化失败，将使用静态爬取: %v", err)
			sc.config.EnableJSRendering = false
		}
	}

	// 创建目标专用的URL队列和结果队列
	urlQueue := make(chan *PageInfo, 100)
	resultQueue := make(chan *PageInfo, 100)
	visitedURLs := make(map[string]bool)
	var visitedMutex sync.RWMutex

	// 添加种子URL到队列
	for _, seedURL := range target.SeedURLs {
		pageInfo := &PageInfo{
			URL:       seedURL,
			Depth:     0,
			ParentURL: "",
			CrawledAt: time.Now(),
		}
		select {
		case urlQueue <- pageInfo:
		default:
			log.Printf("⚠️ URL队列已满，跳过: %s", seedURL)
		}
	}

	// 启动工作协程
	var wg sync.WaitGroup
	stopChan := make(chan struct{})

	// 启动爬取工作协程
	for i := 0; i < sc.config.MaxConcurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for {
				select {
				case <-stopChan:
					return
				case pageInfo := <-urlQueue:
					if pageInfo == nil {
						continue
					}

					// 检查是否已访问
					visitedMutex.RLock()
					visited := visitedURLs[pageInfo.URL]
					visitedMutex.RUnlock()
					if visited {
						continue
					}

					// 检查域名限制
					if !sc.isDomainAllowed(pageInfo.URL, target) {
						continue
					}

					// 检查深度限制
					if pageInfo.Depth > sc.config.MaxDepth {
						continue
					}

					// 执行爬取
					result, err := sc.crawlPage(pageInfo, target)
					if err != nil {
						log.Printf("❌ 爬取失败 %s: %v", pageInfo.URL, err)
						continue
					}

					// 标记为已访问
					visitedMutex.Lock()
					visitedURLs[pageInfo.URL] = true
					visitedMutex.Unlock()

					// 发送结果到处理队列
					select {
					case resultQueue <- result:
					default:
						log.Printf("⚠️ 结果队列已满，丢弃结果: %s", result.URL)
					}

					// 使用新的链接过滤和限制逻辑
					if pageInfo.Depth+1 <= sc.config.MaxDepth {
						filteredLinks := sc.filterAndLimitLinks(result.Links, pageInfo.Depth+1, target)

						for _, link := range filteredLinks {
							// 检查是否已访问
							visitedMutex.RLock()
							visited := visitedURLs[link]
							visitedMutex.RUnlock()

							if !visited {
								newPageInfo := &PageInfo{
									URL:       link,
									Depth:     pageInfo.Depth + 1,
									ParentURL: pageInfo.URL,
									CrawledAt: time.Now(),
								}
								select {
								case urlQueue <- newPageInfo:
								default:
									// 队列已满，跳过
								}
							}
						}
					}
				}
			}
		}()
	}

	// 启动结果处理协程
	wg.Add(1)
	go func() {
		defer wg.Done()

		for {
			select {
			case <-stopChan:
				return
			case result := <-resultQueue:
				if result == nil {
					continue
				}

				// 处理爬取结果
				if err := sc.processResult(result, target); err != nil {
					log.Printf("❌ 处理结果失败 %s: %v", result.URL, err)
				}
			}
		}
	}()

	// 等待一段时间或者队列为空
	timeout := time.After(time.Minute * 5) // 5分钟超时
	ticker := time.NewTicker(time.Second * 5)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			log.Printf("⏰ 智能爬取超时: %s", target.Name)
			close(stopChan)
			wg.Wait()
			return
		case <-ticker.C:
			// 检查队列是否为空
			if len(urlQueue) == 0 && len(resultQueue) == 0 {
				log.Printf("✅ 智能爬取队列已空: %s", target.Name)
				close(stopChan)
				wg.Wait()
				return
			}
		}
	}
}

// filterAndLimitLinks 过滤和限制链接数量
func (sc *SmartKnowledgeCrawler) filterAndLimitLinks(links []string, depth int, target *SmartCrawlTarget) []string {
	if len(links) == 0 {
		return links
	}

	// 1. 基础过滤：移除无效链接
	validLinks := make([]string, 0, len(links))
	for _, link := range links {
		if sc.isValidLink(link) && sc.isDomainAllowed(link, target) {
			validLinks = append(validLinks, link)
		}
	}

	// 2. 检查总链接数限制
	sc.linkCountMutex.Lock()
	if sc.totalLinksProcessed >= sc.config.MaxTotalLinks {
		sc.linkCountMutex.Unlock()
		log.Printf("⚠️ 已达到总链接数限制 (%d)，停止添加新链接", sc.config.MaxTotalLinks)
		return []string{}
	}

	// 3. 检查每层链接数限制
	currentDepthCount := sc.depthLinkCount[depth]
	if currentDepthCount >= sc.config.MaxLinksPerDepth {
		sc.linkCountMutex.Unlock()
		log.Printf("⚠️ 第%d层已达到链接数限制 (%d)，停止添加新链接", depth, sc.config.MaxLinksPerDepth)
		return []string{}
	}
	sc.linkCountMutex.Unlock()

	// 4. 限制每页链接数
	if len(validLinks) > sc.config.MaxLinksPerPage {
		log.Printf("📊 发现 %d 个链接，限制为 %d 个", len(validLinks), sc.config.MaxLinksPerPage)
		validLinks = sc.prioritizeLinks(validLinks, target)[:sc.config.MaxLinksPerPage]
	}

	// 5. 检查剩余可用链接数
	sc.linkCountMutex.Lock()
	remainingTotal := sc.config.MaxTotalLinks - sc.totalLinksProcessed
	remainingDepth := sc.config.MaxLinksPerDepth - sc.depthLinkCount[depth]
	maxAllowed := min(remainingTotal, remainingDepth)

	if len(validLinks) > maxAllowed {
		validLinks = validLinks[:maxAllowed]
		log.Printf("📊 根据限制调整为 %d 个链接", maxAllowed)
	}

	// 6. 更新统计
	sc.totalLinksProcessed += len(validLinks)
	sc.depthLinkCount[depth] += len(validLinks)
	sc.linkCountMutex.Unlock()

	log.Printf("🔗 第%d层添加 %d 个链接 (总计: %d/%d, 本层: %d/%d)",
		depth, len(validLinks), sc.totalLinksProcessed, sc.config.MaxTotalLinks,
		sc.depthLinkCount[depth], sc.config.MaxLinksPerDepth)

	return validLinks
}

// prioritizeLinks 根据优先级模式排序链接
func (sc *SmartKnowledgeCrawler) prioritizeLinks(links []string, target *SmartCrawlTarget) []string {
	switch sc.config.LinkPriorityMode {
	case "keyword":
		return sc.prioritizeByKeywords(links, target.PriorityKeywords)
	case "quality":
		return sc.prioritizeByQuality(links)
	case "random":
		return sc.shuffleLinks(links)
	default:
		return sc.prioritizeByKeywords(links, target.PriorityKeywords)
	}
}

// prioritizeByKeywords 根据关键词优先级排序
func (sc *SmartKnowledgeCrawler) prioritizeByKeywords(links []string, keywords []string) []string {
	if len(keywords) == 0 {
		return links
	}

	type linkScore struct {
		url   string
		score int
	}

	scored := make([]linkScore, 0, len(links))
	for _, link := range links {
		score := 0
		linkLower := strings.ToLower(link)
		for _, keyword := range keywords {
			if strings.Contains(linkLower, strings.ToLower(keyword)) {
				score++
			}
		}
		scored = append(scored, linkScore{url: link, score: score})
	}

	// 按分数降序排序
	sort.Slice(scored, func(i, j int) bool {
		return scored[i].score > scored[j].score
	})

	result := make([]string, len(scored))
	for i, item := range scored {
		result[i] = item.url
	}
	return result
}

// prioritizeByQuality 根据URL质量排序（简单启发式）
func (sc *SmartKnowledgeCrawler) prioritizeByQuality(links []string) []string {
	type linkScore struct {
		url   string
		score int
	}

	scored := make([]linkScore, 0, len(links))
	for _, link := range links {
		score := sc.calculateURLQuality(link)
		scored = append(scored, linkScore{url: link, score: score})
	}

	// 按分数降序排序
	sort.Slice(scored, func(i, j int) bool {
		return scored[i].score > scored[j].score
	})

	result := make([]string, len(scored))
	for i, item := range scored {
		result[i] = item.url
	}
	return result
}

// calculateURLQuality 计算URL质量分数
func (sc *SmartKnowledgeCrawler) calculateURLQuality(url string) int {
	score := 0
	urlLower := strings.ToLower(url)

	// 正面指标
	positiveKeywords := []string{"article", "post", "blog", "news", "content", "detail", "tutorial", "guide"}
	for _, keyword := range positiveKeywords {
		if strings.Contains(urlLower, keyword) {
			score += 2
		}
	}

	// 负面指标
	negativeKeywords := []string{"ad", "advertisement", "popup", "banner", "promo", "download", "pdf", "zip"}
	for _, keyword := range negativeKeywords {
		if strings.Contains(urlLower, keyword) {
			score -= 3
		}
	}

	// URL结构评分
	if strings.Count(url, "/") >= 3 && strings.Count(url, "/") <= 6 {
		score += 1 // 适中的URL深度
	}

	return score
}

// shuffleLinks 随机打乱链接顺序
func (sc *SmartKnowledgeCrawler) shuffleLinks(links []string) []string {
	result := make([]string, len(links))
	copy(result, links)

	for i := len(result) - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		result[i], result[j] = result[j], result[i]
	}

	return result
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// sanitizeContent 清理和验证内容，解决字符编码问题
func (sc *SmartKnowledgeCrawler) sanitizeContent(content string) string {
	if content == "" {
		return content
	}

	// 1. 移除NULL字符和其他控制字符
	content = strings.ReplaceAll(content, "\x00", "")
	content = strings.ReplaceAll(content, "\x01", "")
	content = strings.ReplaceAll(content, "\x02", "")
	content = strings.ReplaceAll(content, "\x03", "")
	content = strings.ReplaceAll(content, "\x04", "")
	content = strings.ReplaceAll(content, "\x05", "")
	content = strings.ReplaceAll(content, "\x06", "")
	content = strings.ReplaceAll(content, "\x07", "")
	content = strings.ReplaceAll(content, "\x08", "")
	// 保留\x09 (tab), \x0A (LF), \x0D (CR)
	content = strings.ReplaceAll(content, "\x0B", "")
	content = strings.ReplaceAll(content, "\x0C", "")
	content = strings.ReplaceAll(content, "\x0E", "")
	content = strings.ReplaceAll(content, "\x0F", "")

	// 2. 确保UTF-8编码有效性
	if !utf8.ValidString(content) {
		// 将无效的UTF-8字符替换为问号
		content = strings.ToValidUTF8(content, "?")
		log.Printf("   ⚠️ 发现无效UTF-8字符，已替换")
	}

	// 3. 移除MySQL不支持的4字节UTF-8字符（emoji等）
	// 如果数据库使用utf8而不是utf8mb4
	content = regexp.MustCompile(`[\x{10000}-\x{10FFFF}]`).ReplaceAllString(content, "")

	// 4. 限制长度避免数据库字段溢出
	maxLength := 65535 // TEXT字段的最大长度
	if len(content) > maxLength {
		content = content[:maxLength]
		log.Printf("   ⚠️ 内容过长，已截断到 %d 字符", maxLength)
	}

	// 5. 清理多余的空白字符
	content = strings.TrimSpace(content)

	return content
}

// createKnowledge 创建知识对象的辅助方法
func (sc *SmartKnowledgeCrawler) createKnowledge(question, answer string, result *PageInfo, target *SmartCrawlTarget, extractionType string) *learning.LearnedKnowledge {
	return &learning.LearnedKnowledge{
		Question:    question,
		Answer:      answer,
		Source:      "crawler",
		Confidence:  float32(result.QualityScore),
		Category:    target.Category,
		Keywords:    target.Keywords,
		Context:     fmt.Sprintf("从网站爬取: %s", result.URL),
		LearnedFrom: "smart_crawler",
		Status:      "approved",
		CreatedAt:   time.Now(),
		Metadata: map[string]interface{}{
			"source_url":      result.URL,
			"crawled_at":      result.CrawledAt,
			"depth":           result.Depth,
			"quality_score":   result.QualityScore,
			"content_length":  len(result.Content),
			"language":        result.Language,
			"extraction_type": extractionType,
		},
	}
}

// generateQuestionFromTitle 从标题生成问题
func (sc *SmartKnowledgeCrawler) generateQuestionFromTitle(title string, target *SmartCrawlTarget) string {
	if title == "" {
		return ""
	}

	// 清理标题
	title = strings.TrimSpace(title)
	title = strings.ReplaceAll(title, "_百度搜索", "")
	title = strings.ReplaceAll(title, " - 百度", "")
	title = strings.ReplaceAll(title, "百度", "")

	// 如果标题已经是问句，直接返回
	if strings.HasSuffix(title, "?") || strings.HasSuffix(title, "？") {
		return title
	}

	// 根据不同类型生成问题
	if strings.Contains(title, "小说") {
		return fmt.Sprintf("什么是%s？", title)
	}

	if strings.Contains(title, "热搜") {
		return "当前有哪些热门话题？"
	}

	if strings.Contains(title, "新闻") {
		return fmt.Sprintf("关于%s的最新消息是什么？", strings.ReplaceAll(title, "新闻", ""))
	}

	// 通用问题生成
	if len(title) > 0 && len(title) < 50 {
		return fmt.Sprintf("什么是%s？", title)
	}

	return ""
}

// extractSpecialSiteKnowledge 提取特殊网站的知识
func (sc *SmartKnowledgeCrawler) extractSpecialSiteKnowledge(result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	url := result.URL
	content := result.Content
	title := result.Title

	// 百度热搜特殊处理
	if strings.Contains(url, "baidu.com") && (strings.Contains(title, "热搜") || strings.Contains(content, "热搜")) {
		return sc.extractBaiduHotSearchKnowledge(result, target)
	}

	// 百度搜索结果特殊处理
	if strings.Contains(url, "baidu.com/s?") {
		return sc.extractBaiduSearchKnowledge(result, target)
	}

	// 新闻网站特殊处理
	if strings.Contains(url, "news") || strings.Contains(title, "新闻") {
		return sc.extractNewsKnowledge(result, target)
	}

	return nil
}

// extractBaiduHotSearchKnowledge 提取百度热搜知识
func (sc *SmartKnowledgeCrawler) extractBaiduHotSearchKnowledge(result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	content := result.Content

	// 提取热搜话题
	hotTopics := []string{}
	lines := strings.Split(content, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 5 && len(line) < 50 && !strings.Contains(line, "百度") && !strings.Contains(line, "登录") {
			// 简单的热搜话题识别
			if strings.Contains(line, "热") || strings.Contains(line, "新") || strings.Contains(line, "沸") {
				hotTopics = append(hotTopics, line)
				if len(hotTopics) >= 10 { // 最多提取10个话题
					break
				}
			}
		}
	}

	if len(hotTopics) > 0 {
		question := "当前百度热搜榜上有哪些热门话题？"
		answer := fmt.Sprintf("根据最新的百度热搜榜，当前热门话题包括：\n%s", strings.Join(hotTopics, "\n"))

		return sc.createKnowledge(question, answer, result, target, "baidu_hot_search")
	}

	return nil
}

// extractBaiduSearchKnowledge 提取百度搜索知识
func (sc *SmartKnowledgeCrawler) extractBaiduSearchKnowledge(result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	title := result.Title
	content := result.Content

	// 从搜索标题中提取关键词
	if strings.Contains(title, "_百度搜索") {
		searchTerm := strings.ReplaceAll(title, "_百度搜索", "")
		searchTerm = strings.TrimSpace(searchTerm)

		if len(searchTerm) > 0 && len(content) > 100 {
			question := fmt.Sprintf("关于%s有什么信息？", searchTerm)
			answer := sc.generateSummary(content)

			if len(answer) > 20 {
				return sc.createKnowledge(question, answer, result, target, "baidu_search")
			}
		}
	}

	return nil
}

// extractNewsKnowledge 提取新闻知识
func (sc *SmartKnowledgeCrawler) extractNewsKnowledge(result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	title := result.Title
	content := result.Content

	if len(title) > 0 && len(content) > 100 {
		question := fmt.Sprintf("关于%s的新闻内容是什么？", title)
		answer := sc.generateSummary(content)

		if len(answer) > 20 {
			return sc.createKnowledge(question, answer, result, target, "news_content")
		}
	}

	return nil
}
