package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 直接连接数据库
	db, err := sql.Open("mysql", "root:park%123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 检查数据库中的数据...")

	// 1. 检查爬取结果表
	fmt.Println("\n📊 1. 检查crawl_results表:")
	var crawlCount int
	err = db.QueryRow("SELECT COUNT(*) FROM crawl_results").Scan(&crawlCount)
	if err != nil {
		fmt.Printf("❌ 查询crawl_results失败: %v\n", err)
	} else {
		fmt.Printf("✅ crawl_results表中有 %d 条记录\n", crawlCount)
	}

	if crawlCount > 0 {
		// 显示最近的几条记录
		rows, err := db.Query(`
			SELECT id, title, LEFT(content, 100) as content_preview, status, crawled_at
			FROM crawl_results 
			ORDER BY crawled_at DESC 
			LIMIT 5
		`)
		if err == nil {
			defer rows.Close()
			fmt.Println("\n最近的爬取结果:")
			for rows.Next() {
				var id int
				var title, contentPreview, status, crawledAt string
				rows.Scan(&id, &title, &contentPreview, &status, &crawledAt)
				fmt.Printf("  ID %d: %s (%s) - %s\n", id, title, status, crawledAt[:16])
				fmt.Printf("    内容预览: %s...\n", contentPreview)
			}
		}

		// 检查是否有包含"百宝箱"的内容
		var baoCount int
		err = db.QueryRow("SELECT COUNT(*) FROM crawl_results WHERE content LIKE '%百宝箱%'").Scan(&baoCount)
		if err == nil {
			fmt.Printf("\n包含'百宝箱'的记录数: %d\n", baoCount)
		}
	}

	// 2. 检查学习知识表
	fmt.Println("\n📚 2. 检查learned_knowledge表:")
	var learnCount int
	err = db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&learnCount)
	if err != nil {
		fmt.Printf("❌ 查询learned_knowledge失败: %v\n", err)
	} else {
		fmt.Printf("✅ learned_knowledge表中有 %d 条记录\n", learnCount)
	}

	if learnCount > 0 {
		// 显示最近的几条记录
		rows, err := db.Query(`
			SELECT id, question, LEFT(answer, 100) as answer_preview, status, created_at
			FROM learned_knowledge 
			ORDER BY created_at DESC 
			LIMIT 5
		`)
		if err == nil {
			defer rows.Close()
			fmt.Println("\n最近的学习知识:")
			for rows.Next() {
				var id int
				var question, answerPreview, status, createdAt string
				rows.Scan(&id, &question, &answerPreview, &status, &createdAt)
				fmt.Printf("  ID %d: %s (%s) - %s\n", id, question, status, createdAt[:16])
				fmt.Printf("    答案预览: %s...\n", answerPreview)
			}
		}

		// 检查是否有包含"百宝箱"的知识
		var baoKnowledgeCount int
		err = db.QueryRow("SELECT COUNT(*) FROM learned_knowledge WHERE question LIKE '%百宝箱%' OR answer LIKE '%百宝箱%'").Scan(&baoKnowledgeCount)
		if err == nil {
			fmt.Printf("\n包含'百宝箱'的学习知识数: %d\n", baoKnowledgeCount)
		}
	}

	// 3. 检查FAQ表
	fmt.Println("\n📋 3. 检查faq表:")
	var faqCount int
	err = db.QueryRow("SELECT COUNT(*) FROM faq").Scan(&faqCount)
	if err != nil {
		fmt.Printf("❌ 查询faq失败: %v\n", err)
	} else {
		fmt.Printf("✅ faq表中有 %d 条记录\n", faqCount)
	}

	if faqCount > 0 {
		// 显示所有FAQ记录
		rows, err := db.Query(`
			SELECT id, question, LEFT(answer, 100) as answer_preview
			FROM faq 
			ORDER BY id
		`)
		if err == nil {
			defer rows.Close()
			fmt.Println("\nFAQ记录:")
			for rows.Next() {
				var id int
				var question, answerPreview string
				rows.Scan(&id, &question, &answerPreview)
				fmt.Printf("  ID %d: %s\n", id, question)
				fmt.Printf("    答案预览: %s...\n", answerPreview)
			}
		}

		// 检查是否有包含"百宝箱"的FAQ
		var baoFAQCount int
		err = db.QueryRow("SELECT COUNT(*) FROM faq WHERE question LIKE '%百宝箱%' OR answer LIKE '%百宝箱%'").Scan(&baoFAQCount)
		if err == nil {
			fmt.Printf("\n包含'百宝箱'的FAQ数: %d\n", baoFAQCount)
		}
	}

	fmt.Println("\n🔍 数据检查完成")
}
